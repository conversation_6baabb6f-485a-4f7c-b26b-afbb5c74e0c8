#include <stdio.h>
#include <lwip/sockets.h>
#include "esp_system.h"
//#include "esp_event_loop.h"
#include "esp_event.h"

#include "esp_spi_flash.h"
#include "esp_wifi.h"

#include "esp_log.h"
#include "esp_err.h"
#include "nvs_flash.h"
#include "nvs.h"

#include "freertos/FreeRTOS.h"
#include "freertos/task.h"
#include "driver/uart.h"
#include "driver/gpio.h"
#include "string.h"
#include "esp_timer.h"
#include "esp_log.h"

#include "esp_system.h"
#include "esp_log.h"
#include "esp_ota_ops.h"

#include "esp_http_client.h"
#include "esp_https_ota.h"
#include "string.h"

#include "nvs.h"
#include "nvs_flash.h"
#include <sys/socket.h>


#include "Lcd.h"
#include "Led.h"
#include "user_wifi.h"
#include "user_TcpClient.h"
#include "common.h"
/*****************************************************************/

/*************************************************主进程函数******************************************************************************/
//Embedded main entry function 
void app_main()
{
    //init run temp data
    _init_station_data();
    _init_run_data();

    esp_err_t err = nvs_flash_init();
    char str_value[100];
    memset(str_value,0,100);
    if (err == ESP_ERR_NVS_NO_FREE_PAGES || err == ESP_ERR_NVS_NEW_VERSION_FOUND)
    {
        sprintf(str_value, "nvs_flash_init error (%s)!\r\n", esp_err_to_name(err));

        ESP_ERROR_CHECK(nvs_flash_erase());

        err = nvs_flash_init();
    }

    bool bret = _read_flash_alldata();
    if(!bret)
    {
        sprintf(str_value, "_read_flash_alldata error (%d)!\r\n", bret);
    }

    err = _uart_init();
    if ((err == ESP_OK))
    {
        //task of receiving from reader or computer by serial communication
        xTaskCreate(_uart_rx_task, g_suartTaskName, 1024 * 10, NULL, configMAX_PRIORITIES, NULL);
    }

    //initial led and beep
    _init_Led();

    _wifi_init_sta();

    //tasek of connnecting server by socket and receive data
    xTaskCreate(&_tcp_client_connect, g_sconrxTaskName, 1024 * 20, NULL, 5, NULL);

    //daily scan timer
    _create_daily_timer();

    //initial reader working parameter
    _init_reader_params();

    //task of scan tag and time consuming tasks
     xTaskCreate(_main_task, g_snetrwTaskName, 1024 * 10, NULL, 10, NULL);
     vTaskDelay(200);

    //远程更新固件线程任务
     xTaskCreate(&_ota_http_task, g_sota_http_task, 1024 * 10, NULL, 5, NULL);
}


//心跳和日常扫描的定时器
 bool _create_daily_timer()
 {
    bool bret = true;

    //initial struct
    esp_timer_create_args_t net_rw_timer =
    {
        .callback = &_daily_timer_handle, 
        .arg = NULL, 				
        .name = g_daily_timer_name 		
    };
    
    esp_err_t err = esp_timer_create(&net_rw_timer, &g_daily_timer_handle);
    err = esp_timer_start_periodic(g_daily_timer_handle, 1000 * 1000);//
    if (err != ESP_OK)
    {
        //ESP_LOGE(TAG, "Failed to start periodic timer: %s", esp_err_to_name(err));
        return bret;
    }
    else
    {
        printf("timer1 cteate and start ok!\r\n");
    }

    return bret;
 }


//定时器回调函数任务
void _daily_timer_handle(void* arg)
{   
    //BEEP控制 
    g_beep_count++ ;
    //LED控制
    g_whitle_count++ ;						 //设备电压
    g_yellow_count++ ;						 //设备电压
    //心跳控制
    g_heart_intervaling++;
    //日常扫描控制
    g_daily_intervaling++;

    //集中扫描控制
    g_focus_intervaling++;

    // 根据在线状态控制白灯：在线则闪烁，离线则常灭
    if (g_online_state == enYES)
    {
        // 正常工作，白灯闪烁
        _switch_white_led(g_whiteled_state, 0);
    }
    else
    {
        // 异常离线，白灯保持熄灭
        if (g_whiteled_state == LED_ON)
        {
            _white_led_onoff(LED_OFF);
            g_whiteled_state = LED_OFF;
        }
    }
}


//扫描标签的线程任务，同时控制LED灯灯闪烁
void _main_task()
{
    bool bret = true;
    //esp_err_t error;
    
    while (1)
    {
        //闪烁LED
//      _switch_white_led(g_whiteled_state,10);

        //处理心跳
        if( (enDONING != g_focus_is_working) )
        {
            if( (g_heart_intervaling >= g_heart_interval )&&(enYES == g_heart_state) )
             {
                bret =_up_station_heart(IS_SOCKET);
             }
        }
        else
        {
            g_heart_intervaling = (g_heart_intervaling<=12000)?g_heart_intervaling:0;    
        }
        //日常扫描 
        _main_daily_scan(IS_SOCKET);
        
        //处理集中扫描
        _main_focus_scan(IS_SOCKET);
        
        vTaskDelay(50);
    }
}


/*************************************************flash部分******************************************************************************/
//从flash读取初始数据
bool _read_flash_alldata(void)
{
    nvs_handle_t my_handle;
    char  str_value[100];
    memset(str_value,0,100);
  
    esp_err_t err = nvs_open(g_namespace, NVS_READWRITE, &my_handle);
    if ((err != ESP_OK))
    {
        sprintf(str_value, "opening nvs error (%s)!\r\n", esp_err_to_name(err));
        nvs_close(my_handle);
        return false;
    }
    else 
    {
        err = nvs_get_u8(my_handle,g_sprotocol_version, &g_protocol_version);
        size_t uslen = DEVID_LEN;
        err = nvs_get_str(my_handle,g_sdevice_id, &g_device_id,&uslen);
        g_device_id[DEVID_LEN-1] = '\0';
        uslen = SSID_LEN;
        err = nvs_get_str(my_handle, g_cswifi_ssid, &g_swifi_ssid, &uslen);
        uslen = PSWD_LEN;
        err = nvs_get_str(my_handle, g_cswifi_password, &g_swifi_password, &uslen);
        uslen = IPADDRESS_LEN;
        err = nvs_get_str(my_handle, g_cserver_ip, &g_sserver_ip, &uslen);
        err = nvs_get_u16(my_handle, g_cserver_port, &g_server_port);
        err = nvs_get_u8(my_handle, g_sbeep_state, &g_beep_state);
        err = nvs_get_u16(my_handle, g_srepeat_filter_time, &g_repeat_filter_time);
        err = nvs_get_u8(my_handle, g_stransparent_transmission, &g_transparent_transmission);
        err = nvs_get_u8(my_handle, g_sheart_state, &g_heart_state);
        err = nvs_get_u16(my_handle, g_sheart_interval, &g_heart_interval);  
        err = nvs_get_u8(my_handle, g_sdaily_scan_state, &g_daily_scan_state);
        err = nvs_get_u8(my_handle, g_sscan_type, &g_scan_type);
        err = nvs_get_u8(my_handle, g_sdaily_way, &g_daily_scan_way);
        err = nvs_get_u16(my_handle, g_sdaily_interval, &g_daily_interval);
        err = nvs_get_u16(my_handle, g_sdaily_time, &g_daily_scantime);
        err = nvs_get_u16(my_handle, g_sfocus_interval, &g_focus_interval);
        err = nvs_get_u16(my_handle, g_sfocus_time, &g_focus_scantime);
        err = nvs_get_u8(my_handle, g_sdown_count, &g_down_count);
        uslen = g_down_count*TAG_LEN;
        err = nvs_get_str(my_handle, g_sdown_tag, (char*)g_down_tag+PACKHEADER_LEN,&uslen);

        nvs_close(my_handle);
    }
    return true;
}


// //将缓存数据全部写入flash
char _write_flash_alldata( )
{
    nvs_handle_t my_handle;
    char str_value[100];
    memset(str_value,0,100);
    esp_err_t err = nvs_open(g_namespace, NVS_READWRITE, &my_handle);
    if (err != ESP_OK) 
    {
        sprintf(str_value, "opening nvs error (%s)!\r\n", esp_err_to_name(err));
        nvs_close(my_handle);
        return RPT_WFLASH;
    }
    else 
    {   
        err = nvs_set_u8(my_handle, g_sprotocol_version, g_protocol_version);
        err = nvs_set_str(my_handle, g_sdevice_id, g_device_id);
        err = nvs_set_str(my_handle, g_cswifi_ssid, g_swifi_ssid);
        err = nvs_set_str(my_handle, g_cswifi_password, g_swifi_password) ;
        err = nvs_set_str(my_handle, g_cserver_ip, g_sserver_ip);
        err = nvs_set_u16(my_handle, g_cserver_port, g_server_port);
        err = nvs_set_u8(my_handle, g_sbeep_state, &g_beep_state);
        err = nvs_set_u16(my_handle, g_srepeat_filter_time, &g_repeat_filter_time);
        err = nvs_set_u8(my_handle, g_stransparent_transmission, g_transparent_transmission);
        err = nvs_set_u8(my_handle, g_sheart_state, g_heart_state);
        err = nvs_set_u16(my_handle, g_sheart_interval, g_heart_interval);
        err = nvs_set_u8(my_handle, g_sdaily_scan_state, g_daily_scan_state);
        err = nvs_set_u8(my_handle, g_sscan_type, g_scan_type);
        err = nvs_set_u8(my_handle, g_sdaily_way, g_daily_scan_way);
        err = nvs_set_u16(my_handle, g_sdaily_interval, g_daily_interval);
        err = nvs_set_u16(my_handle, g_sdaily_time, g_daily_scantime);
        err = nvs_set_u16(my_handle, g_sfocus_interval, g_focus_interval);
        err = nvs_set_u16(my_handle, g_sfocus_time, g_focus_scantime);
        err = nvs_set_u8(my_handle, g_sdown_count, g_down_count);
        if (err != ESP_OK) {
            ESP_LOGE(TAG, "Failed to write tag count to flash (%s)", esp_err_to_name(err));
            nvs_close(my_handle);
            return RPT_WFLASH;
        }

        // 写入标签数据
        if (g_down_count > 0 && g_down_count <= MAX_COUNT) {
            ESP_LOGI(TAG, "Writing tag data to flash, count: %d, data length: %d", g_down_count, g_down_count * TAG_LEN);

            // 打印要写入的标签数据用于调试
            if (g_down_count > 0) {
                ESP_LOGI(TAG, "Writing first tag data: %02X %02X %02X %02X",
                    g_down_tag[PACKHEADER_LEN], g_down_tag[PACKHEADER_LEN+1],
                    g_down_tag[PACKHEADER_LEN+2], g_down_tag[PACKHEADER_LEN+3]);
            }

            // 使用nvs_set_blob保存二进制数据，而不是nvs_set_str
            size_t data_len = g_down_count * TAG_LEN;
            err = nvs_set_blob(my_handle, g_sdown_tag, g_down_tag+PACKHEADER_LEN, data_len);
            if (err != ESP_OK) {
                ESP_LOGE(TAG, "Failed to write tag data to flash (%s)", esp_err_to_name(err));
                nvs_close(my_handle);
                return RPT_WFLASH;
            }
            ESP_LOGI(TAG, "Successfully wrote %d bytes of tag data to flash", data_len);
        } else if (g_down_count > MAX_COUNT) {
            ESP_LOGE(TAG, "Invalid tag count %d > MAX_COUNT %d, not writing to flash", g_down_count, MAX_COUNT);
            nvs_close(my_handle);
            return RPT_WFLASH;
        }

        err = nvs_commit(my_handle);
        if (err != ESP_OK) {
            ESP_LOGE(TAG, "Failed to commit data to flash (%s)", esp_err_to_name(err));
            nvs_close(my_handle);
            return RPT_WFLASH;
        }

        nvs_close(my_handle);
        ESP_LOGI(TAG, "Successfully wrote all data to flash");

        // 写入后立即验证
        // _debug_nvs_tags(); // 函数已删除，注释掉
    }
    return RPT_SUCCESS;
}



//将透传状态写入flash
bool _write_flash_tt_state()
{
    bool bret = true;
    nvs_handle_t my_handle;
    char str_value[100];
    memset(str_value,0,100);
    esp_err_t err = nvs_open(g_namespace, NVS_READWRITE, &my_handle);
    if (err != ESP_OK)
    {
        sprintf(str_value, "opening nvs error (%s)!\r\n", esp_err_to_name(err));
        nvs_close(my_handle);
        return false;
    }
    else
    {
        err = nvs_set_u8(my_handle, g_stransparent_transmission, g_transparent_transmission);
        err = nvs_commit(my_handle);

        nvs_close(my_handle);
    }
    return bret; 
}


//将设备ID写入flash
char _write_flash_deviceid()
{
    char cret = RPT_SUCCESS;
    nvs_handle_t my_handle;
    char str_value[100];
    memset(str_value,0,100);
    esp_err_t err = nvs_open(g_namespace, NVS_READWRITE, &my_handle);
    if (err != ESP_OK)
    {
        sprintf(str_value, "opening nvs error (%s)!\r\n", esp_err_to_name(err));
        nvs_close(my_handle);
        return RPT_FAILED;
    }
    else
    {
        err = nvs_set_str(my_handle, g_sdevice_id, g_device_id);
        err = nvs_commit(my_handle);

        nvs_close(my_handle);
    }
    return cret;
}


//将网络参数写入flash
char  _write_flash_netparams()
{
    char bret = RPT_SUCCESS;
    nvs_handle_t my_handle;
    char str_value[100];
    memset(str_value,0,100);
    esp_err_t err = nvs_open(g_namespace, NVS_READWRITE, &my_handle);
    if (err != ESP_OK)
    {
        sprintf(str_value, "opening nvs error (%s)!\r\n", esp_err_to_name(err));
        nvs_close(my_handle);
        return RPT_FAILED;
    }
    else
    {
        err = nvs_set_str(my_handle, g_cswifi_ssid, g_swifi_ssid);
        err = nvs_set_str(my_handle, g_cswifi_password, g_swifi_password) ;
        err = nvs_set_str(my_handle, g_cserver_ip, g_sserver_ip);
        err = nvs_set_u16(my_handle, g_cserver_port, g_server_port);
        
        err = nvs_commit(my_handle);

        nvs_close(my_handle);
    }
    return bret;
}


/****************************************************串口相关********************************************************************/

//初始化uart
esp_err_t _uart_init( )
{
    uart_config_t uart1_config = {
        .baud_rate = BAUD_RATE,                 //
        .data_bits = UART_DATA_8_BITS,          //
        .parity = UART_PARITY_DISABLE,          //
        .stop_bits = UART_STOP_BITS_1,          //
        .flow_ctrl = UART_HW_FLOWCTRL_DISABLE  
        };


    esp_err_t err = uart_param_config(UART_NUM_1, &uart1_config);
 
    uart_set_pin(UART_NUM_1, TXD1_PIN, RXD1_PIN, UART_PIN_NO_CHANGE, UART_PIN_NO_CHANGE);
    
    uart_driver_install(UART_NUM_1, 1024 * 2, 512 * 2, 0, NULL, 0);

    return err;
}

/**********************************串口读部分***********************************/
//读串口和标签数据线程任务
void _uart_rx_task()
{
    unsigned char recdata[1024];
    memset(recdata,0,1024);
    char str_value[100];
    memset(str_value,0,100);
    bool bret = true;

    while (1) 
    {

        const int rxBytes = uart_read_bytes(UART_NUM_1, recdata, 1024, 10 / portTICK_RATE_MS);
        if (rxBytes >0)
        {
            bret = _receive_comdata(recdata,0,rxBytes);        
            if(!bret)
            {
                sprintf(str_value, "_receive_comdata error (%d)!\r\n", bret);
            }
        }
        else
        {
              
            sprintf(str_value, "uart_read_bytes data len error (%d)!\r\n", rxBytes);
            continue;
        }
    }
}

//解析接收串行数据
bool _receive_comdata( unsigned char* recdata, unsigned short nindex,unsigned short nlen)
{
    bool bret = true;
    // if( (PACKHEADER_LEN>nlen))
    // {
    //    _up_station_errcode(UP_STATION_ERROR,RPT_HEADER,IS_TSS);
    //    return false; 
    // }

    // char str_value[100]; // 未使用，注释掉
    unsigned short i = nindex;
    switch (recdata[i++])//i=0
    {              
        //data from reader or transparent transmission of computer
        case FRAME_HEADER:
        {
            if( enYES == g_transparent_transmission)
            {
                send(g_connect_socket, recdata, nlen, 0);
            }
            else
            {
                bret = _deal_rfidcmd(recdata, 1,nlen);//i=1
            }
            break;
        }
        //command from computer
        case STATION_FRAME_HEADER:
        {       
            bret = _deal_serialcmd(recdata, 1,nlen,IS_SERIAL);//i=1
            break;
        }
        default:
        {
            bret = false;
        }
    }
    
    return bret;
}

//处理串口的命令
bool _deal_serialcmd( unsigned char* recdata, unsigned short index, unsigned short nlen,char source)
{   
    bool bret  = true;
    // if( (PACKHEADER_LEN>nlen)||(1!=index) )
    // {
    //    _up_station_errcode(UP_STATION_ERROR,RPT_HEADER,IS_TSS);
    //    return false; 
    // }
    // if( (TX1_BUF_SIZE < nlen)||(RX1_BUF_SIZE <nlen) )
    // {
    //    _up_station_receipt(recdata[2],RPT_LENGTH,IS_TSS);
    //    return false; 
    // }
    // if(enDONING == g_focus_is_working)
    // {
    //     _up_station_receipt(recdata[2],RPT_BLOCK,IS_TSS);
    //     return false;
    // }

    unsigned short i = index;//i=1
    if (( recdata[i++] == STATION_MY_CMD ) )
    {
        switch(recdata[i++])//i=2
        {
          case SET_STATION_RESET:
            {
                
                bret = _set_station_reset( recdata, i ,  nlen ,source );
                break;
            }
            case GET_STATION_PARAM:
            {
                bret = _get_station_params( recdata, i ,  nlen ,source );
                break;
            }
            case SET_STATION_SAVE_TAG:
            {
                
                bret = _set_station_save_tag( recdata, i ,  nlen ,source  );
                break;
            }
            case GET_STATION_SAVE_TAG:
            {
                
                bret = _get_station_save_tag( recdata, i ,  nlen ,source  );
                break;
            }
            case SET_STATION_BEGIN_FOCUS:
            {
                
                bret = _set_station_begin_focus( recdata, i ,  nlen ,source  );
                break;
            }
            case SET_STATION_DAILYPARAMS:
            {
                
                bret = _set_station_dailyparams( recdata, i ,  nlen ,source  );
                break;
            }
            case SET_STATION_HEARTPARAMS:
            {
                
                bret = _set_station_heartparams( recdata, i ,  nlen ,source );
                break;
            }            
            case SET_STATION_UPDATEFIRMWARE:
            {
                bret = _set_station_update_firmware( recdata, i ,  nlen  ,source );
                break;
            }
            case SET_STATION_ECONOMIZEN:
            {
                
                 bret = _set_station_economizen( recdata, i ,  nlen ,source );
                break;
            }
            case SET_STATION_BEEPON:
            {

                bret = _set_station_beepon( recdata, i ,  nlen ,source );
                break;
            }
            case SET_STATION_FOCUSPARAMS:
            {
                
                bret = _set_station_focusparams( recdata, i ,  nlen ,source  );
                break;
            }
            case SET_STATION_IDLESTATE:
            {
                bret = _set_station_idlestate( recdata, i ,  nlen ,source  );
                break;
            }
            case GET_STATION_HEARTPARAMS:
            {
                bret = _get_station_heartparams( recdata, i ,  nlen ,source  );
                break;
            }
            case GET_STATION_DAILYPARAMS:
            {
                bret = _get_station_dailyparams( recdata, i ,  nlen ,source  );
                break;
            }
            case GET_STATION_FOCUSPARAMS:
            {
                bret = _get_station_focusparams( recdata, i ,  nlen ,source  );
                break;
            }
            case SET_STATION_IDLESCAN:
            {
                bret = _set_station_idlescan( recdata, i ,  nlen ,source  );
                break;
            }
            case GET_STATION_SOFT_VER:
            {
                bret = _get_station_soft_ver( recdata, i ,  nlen ,source  );
                break;
            }
            case SET_STATION_TT_STATE:
            {
                bret = _set_station_tt_state( recdata, i ,  nlen ,source  );
                break;
            }
            case GET_STATION_TT_STATE:
            {
                bret = _get_station_tt_state( recdata, i ,  nlen ,source  );
                break;
            }
            case SET_STATION_EARSE_PARAMS:
            {
                bret = _set_station_earse_params( recdata, i ,  nlen ,source  );
                break;
            }
            case SET_STATION_NETPARAMS:
            {
                bret = _set_station_netparams( recdata, i ,  nlen ,source  );
                break;
            }
            case GET_STATION_NETPARAMS:
            {           
                bret = _get_station_netparams( recdata, i ,  nlen ,source  );
                break;
            }
            case SET_STATION_DEVICEID:
            {
                bret = _set_station_deviceid( recdata, i ,  nlen ,source  );
                break;
            }
            case GET_STATION_DEVICEID:
            {
                bret = _get_station_deviceid( recdata, i ,  nlen ,source  );
                break;
            }  
            case SET_STATION_CLEAR_NVS:
            {
                bret = _set_station_clear_nvs( recdata, i ,  nlen ,source  );
                break;
            }
            case UP_STATION_RESTORE_SETTING:
            {
                bret = _up_station_restore_setting(recdata, i, nlen ,source );
                break;
            }
            default :
            {
                bret = false;
                break;
            }
        }
    }
    else
    {
        bret = false;
    }
    return bret;
}


// /**********************************读卡器部分***********************************/
// //初始化UART的工作参数
bool _init_reader_params()
{
    bool bret = true;

    //stop all,reader entry idle
    //使读写器进入到空闲状态
    _set_reader_stop(50);
	//设置标签上传参数
    _set_reader_upparam(10,g_repeat_filter_time,g_threshold);
	//查询标签上传参数
    _get_reader_upparam(10);
	//设置功率
    _set_reader_power(10);
	//查询功率
    _get_reader_power(10);
	//设置读写器自动空闲模式
    _set_reader_idle(g_idle_time,g_scan_time,20);
	//查询读写器自动空闲模式
    _get_reader_idle(10);

    //_set_reader_one(20);
    //_set_reader_coninue(50);

    return bret;
}


// //处理读卡器的命令
bool  _deal_rfidcmd( unsigned char* recdata, unsigned short index, unsigned short nlen)
{
    bool bret = true;
    // if( (PACKHEADER_LEN>nlen)||(1!=index) )
    // {
    //    _up_station_errcode(UP_STATION_ERROR,RPT_HEADER,IS_TSS);
    //    return false; 
    // }
    // if( (TX1_BUF_SIZE < nlen)||(RX1_BUF_SIZE <nlen) )
    // {
    //    _up_station_receipt(recdata[2],RPT_LENGTH,IS_TSS);
    //    return false; 
    // }
	
    unsigned short i = index;//i=1
    
    if ((FIRST_DEF == recdata[i++] ) && (SECOND_DEF == recdata[i++]))
    {   
        //receipt data or proactively report
        if (THIRD_CONFIG == recdata[i])//i=3
        {
            i++;   //i=4
            i++;//i=5
            switch(recdata[i-1])
            {
                case RECEIVE_QUERY_READER:
                {
                    //5A00010200 0010 00 24 04 0007 00010203040609 00020001 A894
                    //query reader params                    
                    _receive_reader_querycable(recdata,i,nlen);
                    break;
                }
                case RECEIPT_SET_POWER:
                {
                    //5A00010201 00010044A6
                    //set reader power   
                    _receipt_reader_set_power(recdata,i,nlen);    
                    break;
                }
                case RECEIVE_GET_POWER:
                {
                    //5A00010202 0008011E021E031E041E34D2
                    //query reader power
                    _receive_reader_query_power(recdata,i,nlen);                   
                    break;
                }
                case RECEIPT_SET_UPPARAMS:
                {
                    //5A00010209 000100C165
                    //set reader upload mode
                    _receipt_reader_uploadmode(recdata,i,nlen);
                    
                    break;
                }
                case RECEIPT_QUERY_TAG_4:
                {
                    //5A00010210 00010029B5                    
                    //query tag data                    
                    _receipt_reader_query_tag(recdata,i,nlen);
                    break;
                }
                case RECEIVE_GET_UPPARAMS:
                {
                    //5A0001020A 0003000A00D525
                    //query reader upload mode                
                    _receive_reader_query_params(recdata,i,nlen);
                    break;
                }
                case RECEIVE_GET_IDLE:
                {
                    //5A0001020D 0001000B94
                    //query reader idle mode
                    _receive_reader_query_idlemode(recdata,i,nlen);
                    break;
                }
                case RECEIPT_STOP_CMD:
                {
                    //5A000102FF 00010079B1
                    //stop working of reader
                    _receipt_reader_stop_cmd(recdata,i,nlen);
                    break;
                }
                default :
                {
                    //_up_station_errcode(UP_STATION_ERROR,69,IS_TSS);
                    bret = false;
                    break;
                }
            }

        }
        else if( THIRD_CONFIRM == recdata[i])//i=3
        {
            // char temtag[200]; // 未使用，注释掉
            bret = false;
        }
        //tag data or proactively report
        else if (THIRD_RECEIVE == recdata[i])//i=3
        { 
            i++;   //i=4
            i++;   //i=5
            switch(recdata[i-1])//i=4
            {
                case RECEIVE_TAG_DATA:
                {
                    //5A00011200 002B000CE2801160600002094ED74AA6300001014B020003000CE2801160200062A6DAE9092908000E1A5A09645EFC
                    _receive_reader_tag(recdata,i,nlen);          
                    break;
                }
                case RECEIVE_END_UPTAG:
                {
                    //end upload tag data;
                    break;
                }
                default :
                {
                    bret = false;
                    break;
                }
            }  
        }
        else
        {
            bret = false;
        }
    }
    else
    {   
        bret = false;
    }
    return bret;    
}


/*********设置读卡器参数********/
//写读卡器的自动空闲模式
void _set_reader_idle(unsigned short idletime,unsigned short scantime,short time)
{
    //使读写器进入到空闲状态
   // uart_write_bytes(UART_NUM_1, g_reader_set_stop,9);

    unsigned char idledata[15];
    memset( idledata, 0, 15) ;
    memcpy( idledata, g_reader_set_idle, 15);
    char idletimehigh = idletime/256;//9位
    char idletimelow = idletime%256;//10位
    char scantimehigh = scantime/256;//11位
    char scantimelow = scantime%256;//12位
    idledata[9] = idletimehigh;
    idledata[10] = idletimelow;
    idledata[11] = scantimehigh;
    idledata[12] = scantimelow;
    

  
    uart_write_bytes(UART_NUM_1, idledata,15);
    vTaskDelay(time);//1=10ms,50*10=500ms
}   
 //[set]查询读卡器的功率
void _set_reader_power(short time)
{
    //使读写器进入到空闲状态
    //uart_write_bytes(UART_NUM_1, g_reader_set_stop,9);
    //vTaskDelay(time);//1=10ms,50*10=500ms
    uart_write_bytes(UART_NUM_1, g_reader_get_power,9);
    vTaskDelay(time);//1=10ms,50*10=500ms
}
//[set]设置读卡器上传参数
void _set_reader_upparam(short time,unsigned short filter_time,unsigned char thresholdtime)
{
    //使读写器进入到空闲状态
    //uart_write_bytes(UART_NUM_1, g_reader_set_stop,9);
    //vTaskDelay(time);//1=10ms,50*10=500ms
    unsigned char upparam[15];
    memset( upparam, 0, 15) ;
    memcpy( upparam, g_reader_set_upparam, 14);
    upparam[8] = filter_time/256;//8位
    upparam[9] = filter_time%256;//9位
    upparam[10] = thresholdtime;
    uart_write_bytes(UART_NUM_1, g_reader_set_upparam,14);
}
//[set]设置查询读卡器停止读，使读写器进入到空闲状态
void _set_reader_stop(short time)
{
    uart_write_bytes(UART_NUM_1, g_reader_set_stop,9);
    vTaskDelay(time);
    uart_write_bytes(UART_NUM_1, g_reader_set_stop,9);
}
//[set]设置读卡器开始上报单个标签
void _set_reader_one(short time)
{
      //使读写器进入到空闲状态
    //uart_write_bytes(UART_NUM_1, g_reader_set_stop,9);
    //vTaskDelay(time);//1=10ms,50*10=500ms
    uart_write_bytes(UART_NUM_1, g_reader_set_one,17);
}
//[set]设置读卡器开始上报多个标签
void _set_reader_coninue(short time)
{
    //使读写器进入到空闲状态
    //uart_write_bytes(UART_NUM_1, g_reader_set_stop,9);
    //vTaskDelay(time);//1=10ms,50*10=500ms
    uart_write_bytes(UART_NUM_1, g_reader_set_coninue,17);
}



/*******读取读卡器参数******/
//[get]查询读卡器的功率
void _get_reader_power(short time)
{
    //使读写器进入到空闲状态
    //uart_write_bytes(UART_NUM_1, g_reader_set_stop,9);
    vTaskDelay(time);//1=10ms,50*10=500ms
    uart_write_bytes(UART_NUM_1, g_reader_set_power,17);
}
//[get]查询读卡器的上传参数
void _get_reader_upparam(short time)
{
    //使读写器进入到空闲状态
    //uart_write_bytes(UART_NUM_1, g_reader_set_stop,9);
    vTaskDelay(time);//1=10ms,50*10=500ms
    uart_write_bytes(UART_NUM_1, g_reader_get_upparam,9);
    
}
//[get]查询读卡器的空闲模式参数
void _get_reader_idle(short time)
{
    //使读写器进入到空闲状态
    //uart_write_bytes(UART_NUM_1, g_reader_set_stop,9);
    vTaskDelay(time);//1=10ms,50*10=500ms
    uart_write_bytes(UART_NUM_1, g_reader_get_idle,9);
}


/***********receive query reader params**************/
//接收数据-查询读写器RFID能力    5A00010200001000240400070001020304060900020001A894
// 5A：帧头
// 00010200：协议控制字
// 0010：长度
// 00：最小发射功率 0dBm
// 24：最大发射功率 36dBm
// 04：天线数目
// 0007：长度
// 00010203040609：频段列表
// 0002：长度
// 0001：RFID 协议列表 00，ISO18000-6C/EPC C1G2；01，ISO18000-6B
// A894：crc 校验位
bool _receive_reader_querycable( unsigned char* recdata, unsigned short index, unsigned short nlen)
{
    //5A00010200 0010 00 24 04 0007 00010203040609 00020001 A894
    bool bret = true;
    int ii = index;
    char high = recdata[ii++]; 
    char low = recdata[ii++]; 
    // short datalen =  (high<<8)|(low); // 未使用，注释掉
    
    g_device_min_power = recdata[ii++];  //工作频段               
    g_device_max_power = recdata[ii++]; //工作频段
    low = recdata[ii++]; 
    g_antenna_count = (high<<8)|(low);//单个设备连接天线个数
    
    high = recdata[ii++]; 
    low = recdata[ii++]; 
    // int ifir = 0; // 设置但未使用，注释掉
    // ifir = (high<<8)|(low);
    //
    //未使用,未处理


    //
    g_reader_ver = recdata[ii++]; //标签的协议版本

    return bret;
}

/********************query reader power********************/
//接收数据- 查询功率 :         5A000102020008011E021E031E041E34D2
// 5A ： frame header
// 00010202 Protocol control word
// 0008 ：length
// 01 ：天线端口 1 功率 PID
// 1E ：天线 1 端口功率 30
// 02 ：天线端口 2 功率 PID
// 1E ：天线 2 端口功率 30
// 03 ：天线端口 3 功率 PID
// 1E ：天线 3 端口功率 30
// 04 ：天线端口 4 功率 PID
// 1E ：天线 4 端口功率 30
// 34D2 ：crc Check Code
bool _receive_reader_query_power( unsigned char* recdata, unsigned short index, unsigned short nlen)
{
    //5A00010202 0008011E021E031E041E34D2
    bool bret = true;
    g_antenna_count = recdata[6];
    for(int i=0;i<g_antenna_count;i++)
    {
        if(i+1>ANTENNA_COUNT)
        {
            bret = false;
            break;
        }
        g_antenna_power[i] = recdata[8+i*2];
        g_antenna_power[1] = recdata[8+i*2];
        g_antenna_power[2] = recdata[8+i*2];
        g_antenna_power[3] = recdata[8+i*2];
    }
    return bret;
}


/****************query reader upload mode****************/
//接收数据-查询标签上传参数  5A0001020A0003000A00D525
// 5A：帧头
// 0001020A：协议控制字
// 0003：长度
// 000A：重复过滤时间 10*10ms
// 00：RSSI 阈值
// D525：crc 校验码
bool _receive_reader_query_params( unsigned char* recdata, unsigned short index, unsigned short nlen)
{
    //5A0001020A 0003000A00D525
    bool bret = true;
    
    int ii = index;
    char high = 0x00;
    char low = 0x00;
    high = recdata[ii++];
    low = recdata[ii++];
    // int datalen =  (high<<8)|low; // 未使用，注释掉
    high = recdata[ii++];
    low = recdata[ii++];
    g_repeat_filter_time = ((high<<8)|low);
    g_threshold = recdata[ii++];

    return bret;
}

/********************receive tag data of reader *****************/
bool _receive_reader_tag( unsigned char* recdata, unsigned short index, unsigned short nlen)
{
    //5A00011200 002B 000C E2801160600002094ED74AA6300001014B020003000CE2801160200062A6DAE9092908000E1A5A09645EFC
    bool bret = true;
    // int datalen = recdata[8]; // 未使用，注释掉
 
    //check scan state
    if( (enDAILY == g_scan_type))
    {  
        _receive_reader_daily_tag(recdata,index,nlen);
    }
    else if((enFOCUS == g_scan_type))
    {
       _receive_reader_focus_tag(recdata,index,nlen);
    }
    else 
    {
       //_up_station_errcode(QUERY_TAG_CONTINUE,RPT_OTHER,IS_TSS);
        bret = false;
    }
    return bret;
}

/********************receive tag of daily scan*****************/
//Receive ： 5A00011200002B000CE2801160600002094ED74AA6300001014B020003000CE2801160200062A6DAE9092908000E1A5A09645EFC
// 5A ：  frame header
// 00011200 ： 协议控制字
// 002B ： 数据length
// 000C ： EPC length
// E2801160600002094ED74AA6：EPC 码
// 3000 ： PC值
// 01 ： 天线ID
// 01 ： RSSI PID
// 4B ： RSSI 值 75
// 02 ： 标签数据读取结果 PID
// 00 ： 读取成功
// 03 ： 标签TID数据 PID
// 000C ： TIDlength
// E2801160200062A6DAE90929 ： TID数据
// 08 ： 当前频点 PID
// 000E1A5A ： 当前频点 924.250MHz
// 09 ： 当前标签相位 PID
// 64 ： 当前标签相位
// 5EFC ： crc Check Code
bool _receive_reader_daily_tag( unsigned char* recdata, unsigned short index, unsigned short nlen)
{
    //5A 00011200 002B 000C E2801160600002094ED74AA6300001014B020003000CE2801160200062A6DAE9092908000E1A5A09645EFC
    bool bret = true;

    int ii = index;//ii=4
    unsigned char high1 = recdata[ii++];
    unsigned char low1 = recdata[ii++];
    // unsigned short datalen =  (high1<<8)|(low1); // 未使用，注释掉
    // unsigned char high2 = recdata[ii++]; // 未使用，注释掉
    // unsigned char low2 = recdata[ii++]; // 未使用，注释掉
    // unsigned short epclen = (high2<<8)|(low2); // 未使用，注释掉

    int mm=9;//ii=9
    unsigned char tag[13];
    tag[12] = '\0';
    for(int i=0;i<TAG_LEN;i++)
    {
        tag[i]= recdata[mm++];
    }
    bret = _compare_daily_duplitag(tag);
    if(!bret)
    {
        if (g_daily_count+1 <= MAX_COUNT)
        {
            for(int i=0;i<TAG_LEN;i++)
            {
                g_daily_tag[g_daily_index++]  = tag[i];
            }
            g_daily_count++;
        }
    }

    return bret;
}

/*******************receive tag of focus scan****************/
//Receive ： 5A00011200002B000CE2801160600002094ED74AA6300001014B020003000CE2801160200062A6DAE9092908000E1A5A09645EFC
// 5A ：  frame header
// 00011200 ： 协议控制字
// 002B ： 数据length
// 000C ： EPC length
// E2801160600002094ED74AA6：EPC 码
// 3000 ： PC值
// 01 ： 天线ID
// 01 ： RSSI PID
// 4B ： RSSI 值 75
// 02 ： 标签数据读取结果 PID
// 00 ： 读取成功
// 03 ： 标签TID数据 PID
// 000C ： TIDlength
// E2801160200062A6DAE90929 ： TID数据
// 08 ： 当前频点 PID
// 000E1A5A ： 当前频点 924.250MHz
// 09 ： 当前标签相位 PID
// 64 ： 当前标签相位
// 5EFC ： crc Check Code
bool _receive_reader_focus_tag( unsigned char* recdata, unsigned short index, unsigned short nlen)
{
    //5A 00011200 002B 000C E2801160600002094ED74AA6300001014B020003000CE2801160200062A6DAE9092908000E1A5A09645EFC
    bool bret = true;

    int ii = index;//ii=4
    char high1 = recdata[ii++];
    char low1 = recdata[ii++];
    // int datalen =  (high1<<8)|(low1); // 未使用，注释掉
    char high2 = recdata[ii++];
    char low2 = recdata[ii++];
    // int epclen = (high2<<8)|(low2); // 未使用，注释掉

    // int mm=9;//ii=9
    // unsigned char tag[12];
    // memset(tag,0,12);
    // for(int i=0;i<TAG_LEN;i++)
    // {
    //     tag[i]= recdata[mm++];
    // }
    bret = _compare_focus_duplitag(recdata+9);
    if(!bret)
    {
        if (g_daily_count+1 <= MAX_COUNT)
        { 
            for(int i=0;i<TAG_LEN;i++)
            {
                g_focus_tag[g_focus_index++]  = recdata[i+9];
            }
            g_focus_count++;
        }
    }

    return bret;
}

// bool _receive_reader_focus_tag( unsigned char* recdata, unsigned short index, unsigned short nlen)
// {
//     //5A00011200002B000CE2801160600002094ED74AA6300001014B020003000CE2801160200062A6DAE9092908000E1A5A09645EFC
//     bool bret = true;
//     g_heart_is_send = enCOMPLETE;
 
//     int ii = index;//ii=3
//     char high = recdata[ii++];
//     char low = recdata[ii++];
//     int datalen =  (high<<8)|(low);
//     high = recdata[ii++];
//     low = recdata[ii++];
//     int epclen = (high<<8)|(low);

//     bool bcheck = true;
//     int ncheck = 0,ntemp = 0;;

//     int ntemp = 0;
//     for (int n = 0;n < g_focus_count;n++)
//     {
//         //Check for repeatability
//         int t = 0;
//         for(int i=0;i<TAG_LEN;i++)
//         {   
//             t = ii;
//             if(g_focus_tag[g_daily_index++] == recdata[t++]) 
//             {
//                 ntemp = 1;
//             }
//             else
//             {
//                 ntemp = 0;
//             }
//             ncheck = ncheck|ntemp<<1;
//         }
//     }
//     bcheck = ncheck&4095;
//     if(!bcheck)
//     {
//         //check array overflow
//         int nix = RX1_BUF_SIZE;

//         if (g_daily_count+1 <= MAX_COUNT)
//         {       
//             for(int i=0;i<TAG_LEN;i++)
//             {
//                g_focus_tag[nix++] = recdata[ii++];
//             }    
//             g_daily_count++;
//         }
//     }

//     g_is_netrec = enCOMPLETE;
//     g_scan_type = enDAILY;
    
//     return bret;
// }

/*******从读卡器接收回执******/

/*******************RECEIPT 设置标签上传参数****************/
// RECEIPT 设置标签上传参数 ： 5A00010209000100C165
// 5A ：  frame header
// 00010209 ： 协议控制字
// 0001 ： length
// 00 ： 设置结果，设置成功
// C165 ： crc Check Code
bool _receipt_reader_uploadmode(unsigned char* recdata, unsigned short index, unsigned short nlen)
{
    bool bret = true;
    return bret;
}

/*******************RECEIPT 设置功率****************/
//RECEIPT 设置功率 :  5A0001020100010044A6
// 5A ： frame header
// 00010201 Protocol control word
// 0001 ：length
// 00 ：设置结果 设置成功
// 44A6 ： crc Check Code
bool _receipt_reader_set_power( unsigned char* recdata, unsigned short index, unsigned short nlen)
{
    bool bret = true;
   
    return bret;
}


/*******************RECEIPT 停止命令****************/
// RECEIPT 停止命令 ： 5A000102FF00010079B1
// 解析 ：5A ：帧头
// 000102FF ：协议控制字
// 0001 ：长度
// 00 ：查询结果，停止成功
// 79B1 ：crc 校验码
// RECEIPT 停止命令
bool _receipt_reader_stop_cmd( unsigned char* recdata, unsigned short index, unsigned short nlen)
{
    bool bret = true;
    return bret;
}

/*******************查询标签的回执数据，来自于读卡器****************/
// RECEIPT 单次查询 ： 5A0001021000010029B5
// 解析 ：5A ： frame header
// 00010210 Protocol control word
// 0001 ：length
// 00 ：读操作设置结果，设置成功
// 29B5 ：crc Check Code


// RECEIPT 循环查询 ：      5A0001021000010029B5
// 解析 ：5A ： frame header
// 00010210 Protocol control word
// 0001 ：length
// 00 ：读操作设置结果，设置成功
// 29B5 ：crc Check Code

//RECEIPT 开始上传id :    5A0001021000010029B5    
// 5A ：  frame header
// 00010210 ： 协议控制字
// 0001 ： length
// 00 ： 读操作设置结果，设置成功
// 29B5 ： crc Check Code

//RECEIPT 结束上id :          5A0001120100010040FC
// 5A ： frame header
// 00011201 Protocol control word
// 0001 ：数据length
// 01 ：读卡结束原因，表示收到停止指令
// 50DD ：crc Check Code
bool _receipt_reader_query_tag( unsigned char* recdata, unsigned short index, unsigned short nlen)
{
    bool bret = true;
    if(0x00 == recdata[7])
    {
        //read success;
    }
    return bret;
}


/********************query reader idle mode ********************/
//查询读写器自动空闲模式  5A0001020E000301000AAA35
// 5A：帧头
// 0001020E：协议控制字
// 0003：长度
// 01：自动空闲模式使能
// 000A：自动空闲时间 10*10ms
// AA35：crc 校验码
bool _receive_reader_query_idlemode( unsigned char* recdata, unsigned short index, unsigned short nlen)
{
    //5A000102 0E 00030 1000AAA35
    bool bret = true;

    int ii = index;
    char high = recdata[ii++];
    char low = recdata[ii++];
    // int datalen =  (high<<8)|(low); // 未使用，注释掉
    g_idle_state = recdata[ii++];

    high = recdata[ii++];
    low = recdata[ii++];
    g_idle_time = ((high<<8)|(low));

    return bret;
}


//对收到的扫描标签去重
bool _compare_daily_duplitag(unsigned char* rec)
{  
    bool bret = false;
    
    for (int n = 0;n < g_daily_count;n++)
    {     
        int j = PACKHEADER_LEN +TAG_LEN*n;
        int m = 0;
        if((rec[m++]==g_daily_tag[j++])&&
            (rec[m++]==g_daily_tag[j++])&&
            (rec[m++]==g_daily_tag[j++])&&
            (rec[m++]==g_daily_tag[j++])&&
            (rec[m++]==g_daily_tag[j++])&&
            (rec[m++]==g_daily_tag[j++])&&
            (rec[m++]==g_daily_tag[j++])&&
            (rec[m++]==g_daily_tag[j++])&&
            (rec[m++]==g_daily_tag[j++])&&
            (rec[m++]==g_daily_tag[j++])&&
            (rec[m++]==g_daily_tag[j++])&&
            (rec[m++]==g_daily_tag[j++]))
        {
            bret = true;
            break;
        }
    }
    return bret;
}

//对收到的扫描标签去重
bool _compare_focus_duplitag(unsigned char* rec)
{  
    bool bret = false;  
    //Check for repeatability
    
    for (int n = 0;n < g_focus_count;n++)
    {
        int j = PACKHEADER_LEN +TAG_LEN*n;
        int m = 0;
        if((rec[m++]==g_focus_tag[j++])&&
            (rec[m++]==g_focus_tag[j++])&&
            (rec[m++]==g_focus_tag[j++])&&
            (rec[m++]==g_focus_tag[j++])&&
            (rec[m++]==g_focus_tag[j++])&&
            (rec[m++]==g_focus_tag[j++])&&
            (rec[m++]==g_focus_tag[j++])&&
            (rec[m++]==g_focus_tag[j++])&&
            (rec[m++]==g_focus_tag[j++])&&
            (rec[m++]==g_focus_tag[j++])&&
            (rec[m++]==g_focus_tag[j++])&&
            (rec[m++]==g_focus_tag[j++]))
        {
            bret = true;
            break;
        }
    }
    return bret;
}
//注意：CRC16 计算不包含帧头
unsigned short CRC16_XMODEM (unsigned char *ptr, int len)
{
    unsigned int i;
    unsigned short crc = 0x0000;
    
    while(len--)
    {
        crc ^= (unsigned short)(*ptr++) << 8;
        for (i = 0; i < 8; ++i)
        {
            if (crc & 0x8000)
            crc = (crc << 1) ^ 0x1021;
        else
            crc <<= 1;
        }
    }
    return crc;
}

/*************************************************网络部分******************************************************************************/

//初始化运行初始数据
void _init_run_data()
{
    g_hardware_version = DEFAULT_HARDWARE_VER;
    g_firmware_version = DEFAULT_FIRMWARE_VER;
    g_reader_ver = 0x01;

    g_voltage = 0x05;
    g_economizen = 0x00;
    g_device_min_power = 0x00;
    g_device_max_power = 0x00;
    g_antenna_count = 0x01;
    memset(g_antenna_power,0,ANTENNA_COUNT);
    g_threshold = 0x00;
    g_idle_state = 0x01;
    g_scan_time = 1500;
    g_idle_time = 6000;


    g_online_state= enNO;
    g_update_state = enNO;
    g_is_netrec = enCOMPLETE;
    g_is_netsend = enCOMPLETE;
    g_heart_intervaling = 0;
    g_heart_is_send = enIDLE;
    memset(g_heartdata,0,MAX_HEART_LEN);

    g_scan_type = enUNUSED;
    g_daily_intervaling = 0;
    g_daily_is_working = enIDLE;
    g_daily_scaning_state = enIDLE;
    g_daily_rec_state = enIDLE;
    g_daily_count = 0;
    g_daily_index = PACKHEADER_LEN;
    memset(g_daily_tag,0,RX1_BUF_SIZE);
    g_focus_intervaling = 0;
    g_focus_is_working = enIDLE;
    g_focus_scaning_state = enIDLE;
    g_focus_rec_state = enIDLE;
    g_focus_count = 0;
    g_focus_index = PACKHEADER_LEN;
    memset(g_focus_tag,0,RX1_BUF_SIZE);


    g_down_rec_state = enIDLE;
    g_down_save_state = enIDLE;
    g_down_count = 0;  // 明确初始化为0
    g_down_index = PACKHEADER_LEN;
    memset(g_down_tag, 0, RX1_BUF_SIZE);  // 清空标签数据
}


//initial station data,and read flash,rewrite it
void _init_station_data()
{
    g_protocol_version = DEFAULT_PROTOCOL_VER;
    for(int i=0;i<(DEVID_LEN-1);i++)//
    {
        g_device_id[i] = g_sdefault_device_id[i];
    } 
    g_device_id[DEVID_LEN-1] = '\0';
    memcpy(g_swifi_ssid,g_sdefault_ssid,strlen(g_sdefault_ssid));		    
    memcpy(g_swifi_password,g_sdefault_password,strlen(g_sdefault_password));	  
    memcpy(g_sserver_ip,g_sdefault_ip,strlen(g_sdefault_ip));
    g_server_port = g_ndefault_port;

    g_beep_state = PART_STATE_OFF;
    g_repeat_filter_time = 20;    

    g_transparent_transmission = enNO;    
    g_heart_state = enYES;
    g_heart_interval = 40;	 
    		
    g_daily_scan_state = enYES;	
    g_daily_scan_way = enIandS;	
    g_daily_interval = 105;	
    g_daily_scantime = 15;
    g_focus_interval = 4;
    g_focus_scantime = 20;
    g_down_count = 0;
    memset(g_down_tag,0,RX1_BUF_SIZE);	
}

/**********************************socket部分*************************************/

//解析接收网络数据
bool _receive_netdata( unsigned char* recdata, unsigned short index, unsigned short nlen)
{
    bool bret  = true;
    // if( (PACKHEADER_LEN>nlen) )
    // {
    //    _up_station_errcode(UP_STATION_ERROR,RPT_HEADER,IS_SOCKET);
    //    return false; 
    // }
    // if( (TX1_BUF_SIZE < nlen)||(RX1_BUF_SIZE <nlen) )
    // {
    //    _up_station_receipt(recdata[2],RPT_LENGTH,IS_SOCKET);
    //    return false; 
    // }
    // if(enDONING == g_focus_is_working)
    // {
    //     _up_station_receipt(recdata[2],RPT_BLOCK,IS_SOCKET);
    //     return false;
    // }

    char str_value[100];
    memset(str_value,0,100);
    
    int ii = index;
    switch (recdata[ii++])//i=0
    {
        //data from reader or transparent transmission of computer
        case FRAME_HEADER:
        {
            if( enYES == g_transparent_transmission)
            {
                uart_write_bytes(UART_NUM_1, recdata,nlen);
            }
            else
            {
                //_up_station_errcode(UP_STATION_ERROR,RPT_NTTERR,IS_SOCKET);
                bret = false;
            }
            break;
        }
        //command from computer
        case STATION_FRAME_HEADER:
        {
            bret = _deal_netcmd(recdata, ii ,nlen, IS_SOCKET);//ii=1
            break;
        }
        default:
        {	
	        //_up_station_errcode(17,17,IS_SERIAL);
            bret = false;
        }
    }
    return bret;
}

//处理上位机的命令
bool _deal_netcmd( unsigned char* recdata, unsigned short index, unsigned short nlen,char source)
{
    bool bret  = true;
    unsigned short i = index;//i=1

    if (( recdata[i++] == STATION_MY_CMD ) )
    {
        switch(recdata[i++])//i=2
        {
          case SET_STATION_RESET:
            {
                
                bret = _set_station_reset( recdata, i ,  nlen,source );
                break;
            }
            case GET_STATION_PARAM:
            {
                bret = _get_station_params( recdata, i ,  nlen,source  );
                break;
            }
            case SET_STATION_SAVE_TAG:
            {
                
                bret = _set_station_save_tag( recdata, i ,  nlen ,source  );
                break;
            }
            case GET_STATION_SAVE_TAG:
            {
                
                bret = _get_station_save_tag( recdata, i ,  nlen ,source  );
                break;
            }
            case SET_STATION_BEGIN_FOCUS:
            {
                bret = _set_station_begin_focus( recdata, i ,  nlen ,source );
                break;
            }
            case SET_STATION_DAILYPARAMS:
            {
                
                bret = _set_station_dailyparams( recdata, i ,  nlen ,source   );
                break;
            }
            case SET_STATION_HEARTPARAMS:
            {
                
                bret = _set_station_heartparams( recdata, i ,  nlen ,source  );
                break;
            }
            case SET_STATION_UPDATEFIRMWARE:
            {
                
                bret = _set_station_update_firmware( recdata, i ,  nlen ,source  );
                break;
            }
            case SET_STATION_ECONOMIZEN:
            {
                
                 bret = _set_station_economizen( recdata, i ,  nlen ,source );
                break;
            }
            case SET_STATION_BEEPON:
            {

                bret = _set_station_beepon( recdata, i ,  nlen ,source );
                break;
            }
            case SET_STATION_FOCUSPARAMS:
            {
                
                bret = _set_station_focusparams( recdata, i ,  nlen ,source  );
                break;
            }
            case SET_STATION_IDLESTATE:
            {
                bret = _set_station_idlestate( recdata, i ,  nlen ,source  );
                break;
            }
            case GET_STATION_HEARTPARAMS:
            {
                bret = _get_station_heartparams( recdata, i ,  nlen ,source  );
                break;
            }
            case GET_STATION_DAILYPARAMS:
            {
                bret = _get_station_dailyparams( recdata, i ,  nlen ,source  );
                break;
            }
            case GET_STATION_FOCUSPARAMS:
            {
                bret = _get_station_focusparams( recdata, i ,  nlen ,source  );
                break;
            }
            case SET_STATION_IDLESCAN:
            {
                bret = _set_station_idlescan( recdata, i ,  nlen ,source  );
                break;
            }
            case GET_STATION_SOFT_VER:
            {
                bret = _get_station_soft_ver( recdata, i ,  nlen ,source  );
                break;
            }
            case SET_STATION_TT_STATE:
            {
                bret = _set_station_tt_state( recdata, i ,  nlen ,source  );
                break;
            }
            case GET_STATION_TT_STATE:
            {
                bret = _get_station_tt_state( recdata, i ,  nlen  ,source );
                break;
            }
            case SET_STATION_EARSE_PARAMS:
            {
                //不支持
                bret = _set_station_earse_params( recdata, i ,  nlen ,source  );
                break;
            }
            case SET_STATION_NETPARAMS:
            {
                bret = _set_station_netparams( recdata, i ,  nlen ,source  );
                break;
            }
            case GET_STATION_NETPARAMS:
            {
                bret = _get_station_netparams( recdata, i ,  nlen  ,source );
                break;
            }
            case SET_STATION_DEVICEID:
            {
                bret = _set_station_deviceid( recdata, i ,  nlen ,source  );
                break;
            }
            case GET_STATION_DEVICEID:
            {
                bret = _get_station_deviceid( recdata, i ,  nlen ,source  );
                break;
            }  
            case SET_STATION_CLEAR_NVS:
            {
                bret = _set_station_clear_nvs( recdata, i ,  nlen ,source  );
                break;
            }
            case UP_STATION_RESTORE_SETTING:
            {
                bret = _up_station_restore_setting( recdata, i ,  nlen ,source  );
                break;
            }
            
            default :
            {
                //_up_station_errcode(UP_STATION_ERROR,RPT_OTHER,source);
                break;
            }
        }
    }
    else
    {
        //_up_station_errcode(UP_STATION_ERROR,16,source);
        return false;
    }
    return bret;
}

//[下行]复位操作/手动模式
bool _set_station_reset( unsigned char* recdata, unsigned short index, unsigned short nlen ,char source)
{ 
    bool bret = false;    
    if( (FRAME_LEN!=index)||(PACKHEADER_LEN!=nlen) )
    {
        _up_station_receipt(GET_STATION_PARAM,RPT_RECLEN,source);
        return false;
    }
    
    char err = RPT_SUCCESS;
    int ii = index;
    unsigned char high = recdata[ii++];
    unsigned char low = recdata[ii++];
    int ndatalen = (high<<8)|low;
    if( 0 != ndatalen )
    {
        bret = false;
        _up_station_receipt(SET_STATION_RESET,RPT_LENGTH,source);
        return false;   
    }
    else
    {
        esp_restart(); 
        bret = _up_station_receipt(SET_STATION_RESET,RPT_SUCCESS,source);    
        return bret;     
    }
    return false;
}


//[下行]查询基站的工作参数  
bool _get_station_params( unsigned char* recdata, unsigned short index, unsigned short nlen ,char source)
{
    bool bret = false;    
    if( (FRAME_LEN!=index)||(PACKHEADER_LEN!=nlen) )
    {
        _up_station_receipt(GET_STATION_PARAM,RPT_RECLEN,source);
        return false;
    }
    
    unsigned char high = recdata[index];
    unsigned char low = recdata[index+1];
    int nrxdtlen = (high<<8)|low;
    if( 0 != nrxdtlen )
    {
        bret = _up_station_receipt(GET_STATION_PARAM,RPT_SUCCESS,source);    
        return bret;
    }
    else
    {
        //发送缓冲区
        int packagelen = 35;//
        unsigned char     txbuf[packagelen+1];         
        memset(txbuf,0,packagelen+1) ;     

        int ntotal = copy_station_params_to_buf( txbuf,packagelen+1 );

        if(ntotal == packagelen)
        {
           bret =  _reply_data(txbuf,ntotal,GET_STATION_PARAM,source);
           return bret;
        }
        else
        {
            bret = _up_station_receipt(GET_STATION_PARAM,RPT_TXSMALL,source);
            return false;
        }
    }
    return false;
}

//[下行]设置基站管理的标签数据
bool _set_station_save_tag( unsigned char* recdata, unsigned short index, unsigned short nlen ,char source)
{
    bool bret = true;
    if( (FRAME_LEN!=index) )
    {
        _up_station_receipt(SET_STATION_SAVE_TAG,RPT_RECLEN,source);
        return false;
    }

    int ii = index ;
    unsigned char high = recdata[ii++];
    unsigned char low = recdata[ii++];
    int nrxdtlen = ( high << 8)|low;

    ESP_LOGI(TAG, "Set station save tag: data_len=%d, total_len=%d", nrxdtlen, nlen);

    if( (PACKHEADER_LEN+nrxdtlen) != nlen )
    {
        ESP_LOGE(TAG, "Length mismatch: expected=%d, actual=%d", PACKHEADER_LEN+nrxdtlen, nlen);
        _up_station_receipt(SET_STATION_SAVE_TAG,RPT_LENGTH,source);
        return false;
    }
    if( (PACKHEADER_LEN+nrxdtlen) >= RX1_BUF_SIZE )
    {
        ESP_LOGE(TAG, "Data too large: %d >= %d", PACKHEADER_LEN+nrxdtlen, RX1_BUF_SIZE);
        _up_station_receipt(SET_STATION_SAVE_TAG,RPT_PACKLEN,source);
        return false;
    }

    // 检查标签数量是否合理
    if (nrxdtlen % TAG_LEN != 0) {
        ESP_LOGE(TAG, "Invalid tag data length: %d not divisible by TAG_LEN %d", nrxdtlen, TAG_LEN);
        _up_station_receipt(SET_STATION_SAVE_TAG,RPT_PACKAGE,source);
        return false;
    }

    int expected_count = nrxdtlen / TAG_LEN;
    if (expected_count > MAX_COUNT) {
        ESP_LOGE(TAG, "Too many tags: %d > MAX_COUNT %d", expected_count, MAX_COUNT);
        _up_station_receipt(SET_STATION_SAVE_TAG,RPT_PACKLEN,source);
        return false;
    }

    g_down_index = PACKHEADER_LEN;
    g_down_count = 0;
    memset(g_down_tag,0,RX1_BUF_SIZE);
    for(int i=1;i<=nrxdtlen;i++)
    {
        //留出包头长度空着
        g_down_tag[g_down_index++] = recdata[ii++];
        if(i%TAG_LEN == 0)
        {
            g_down_count++;
        }
    }
    g_down_tag[3] = (g_down_count*TAG_LEN)/256;
    g_down_tag[4] = (g_down_count*TAG_LEN)%256;

    ESP_LOGI(TAG, "Processed tags: count=%d, expected=%d, data_len=%d", g_down_count, expected_count, g_down_index-PACKHEADER_LEN);

    if( g_down_count*TAG_LEN == (g_down_index-PACKHEADER_LEN) && g_down_count == expected_count )
    {
        char err = _write_flash_alldata( );
        if (err == RPT_SUCCESS) {
            ESP_LOGI(TAG, "Successfully saved %d tags to flash", g_down_count);
        } else {
            ESP_LOGE(TAG, "Failed to save tags to flash, error: %d", err);
        }
        bret = _up_station_receipt(SET_STATION_SAVE_TAG,(unsigned char)err,source);
        return bret;
    }
    else
    {
        ESP_LOGE(TAG, "Tag data validation failed");
        _up_station_receipt(SET_STATION_SAVE_TAG,RPT_PACKAGE,source);
        return false;
    }
    return false;
}

//[下行]查询基站管理的标签数据
bool _get_station_save_tag( unsigned char* recdata, unsigned short index, unsigned short nlen ,char source)
{
    bool bret = true;
    if( (FRAME_LEN!=index)||(PACKHEADER_LEN!=nlen) )
    {
        _up_station_receipt(GET_STATION_SAVE_TAG,RPT_RECLEN,source);
        return false;
    }
    int i = index;
    unsigned char high = recdata[i++];
    unsigned char low = recdata[i++];
    int nrxdtlen = (high<<8)|low;

    if( (0 == nrxdtlen) )
    {
        ESP_LOGI(TAG, "Query saved tags: count=%d, index=%d", g_down_count, g_down_index);

        g_down_tag[0] = STATION_FRAME_HEADER;
        g_down_tag[1] = STATION_MY_CMD;
        g_down_tag[2] = GET_STATION_SAVE_TAG;

        // 确保包头中的数据长度字段正确
        if (g_down_count > 0) {
            // 修复g_down_index如果它不正确
            int expected_index = PACKHEADER_LEN + g_down_count * TAG_LEN;
            if (g_down_index != expected_index) {
                ESP_LOGW(TAG, "Fixing g_down_index from %d to %d", g_down_index, expected_index);
                g_down_index = expected_index;
            }

            g_down_tag[3] = (g_down_count*TAG_LEN)/256;
            g_down_tag[4] = (g_down_count*TAG_LEN)%256;
            ESP_LOGI(TAG, "Returning %d tags, data length: %d, g_down_index: %d", g_down_count, g_down_count*TAG_LEN, g_down_index);

            // 打印返回的标签数据用于调试
            if (g_down_count > 0) {
                ESP_LOGI(TAG, "Returning first tag data: %02X %02X %02X %02X",
                    g_down_tag[PACKHEADER_LEN], g_down_tag[PACKHEADER_LEN+1],
                    g_down_tag[PACKHEADER_LEN+2], g_down_tag[PACKHEADER_LEN+3]);
            }
        } else {
            g_down_tag[3] = 0;  // 数据长度高字节
            g_down_tag[4] = 0;  // 数据长度低字节
            g_down_index = PACKHEADER_LEN;  // 确保索引正确
            ESP_LOGI(TAG, "No tags to return, sending %d bytes", g_down_index);
        }

        // 调试：打印发送的数据
        ESP_LOGI(TAG, "Sending response: %02X %02X %02X %02X %02X (total %d bytes)",
                 g_down_tag[0], g_down_tag[1], g_down_tag[2], g_down_tag[3], g_down_tag[4], g_down_index);

        //回复基站管理的标签数据
        bret = _reply_data(g_down_tag,g_down_index,GET_STATION_SAVE_TAG,source);
        return bret;
    }
    else
    {
        bret = _up_station_receipt(GET_STATION_SAVE_TAG,RPT_LENGTH,source);
        return false;
    }
    return false;
}

//[下行]请求开始集中扫描
bool _set_station_begin_focus( unsigned char* recdata, unsigned short index, unsigned short nlen ,char source)
{
    bool bret  = true;
    if( (FRAME_LEN!=index)||(PACKHEADER_LEN!=nlen) )
    {
        _up_station_receipt(SET_STATION_BEGIN_FOCUS,RPT_RECLEN,source);
        return false;
    }
    if( enDONING == g_focus_is_working)
    {
        _up_station_receipt(SET_STATION_BEGIN_FOCUS,RPT_BLOCK,source);
        return false;
    }
    int i = index;
    unsigned char high = recdata[i++];
    unsigned char low = recdata[i++];
    int nrxdtlen = (high<<8)|low;
    if( 0 == nrxdtlen )
    {  
        _begin_focus_scan();
        bret = _up_station_receipt(SET_STATION_BEGIN_FOCUS,RPT_SUCCESS,source);
        return bret;
    }
    else
    {    
        bret = _up_station_receipt(SET_STATION_BEGIN_FOCUS,RPT_LENGTH,source); 
        return false;
    }
    return false;
}


//[下行]设置日常扫描参数
bool _set_station_dailyparams( unsigned char* recdata, unsigned short index, unsigned short nlen ,char source)
{
    bool bret  = true;
    if( (FRAME_LEN!=index)||((PACKHEADER_LEN+6)!=nlen) )
    {
        _up_station_receipt(SET_STATION_DAILYPARAMS,RPT_RECLEN,source);
        return false;
    }
    
    unsigned char err = RPT_SUCCESS;
    int ii = index;
    unsigned char high = recdata[ii++];
    unsigned char low = recdata[ii++];
    int nrxdtlen = (high<<8)|low;
    if( (PACKHEADER_LEN+nrxdtlen) != nlen )
    {
        _up_station_receipt(SET_STATION_DAILYPARAMS,RPT_LENGTH,source);
        return false;
    }

    if( 6 == nrxdtlen )
    {
        high = (recdata[ii++]);
        low = (recdata[ii++]);
        g_daily_interval = ((high<<8)|low);
        high = (recdata[ii++]);
        low = (recdata[ii++]);	
        g_daily_scantime = ((high<<8)|low);
        g_daily_scan_state = recdata[ii++];
        g_daily_scan_way = recdata[ii++];

        g_idle_time = g_daily_interval;   
        g_scan_time = g_daily_scantime;
    
        err = _write_flash_alldata( );
        bret = _up_station_receipt(SET_STATION_DAILYPARAMS,err,source);  
        return true;
    }
    else
    {
        bret = _up_station_receipt(SET_STATION_DAILYPARAMS,RPT_LENGTH,source);    
        return false;
    }

    return false;
}

//[下行]设置心跳参数
bool _set_station_heartparams( unsigned char* recdata, unsigned short index, unsigned short nlen,char source )
{
    bool bret  = true;
    if( (FRAME_LEN!=index)||((PACKHEADER_LEN+3)!=nlen) )
    {
        _up_station_receipt(SET_STATION_HEARTPARAMS,RPT_RECLEN,source);
        return false;
    }
    unsigned char err = RPT_SUCCESS;
    int ii = index;
    unsigned char high = recdata[ii++];
    unsigned char low = recdata[ii++];
    int nrxdtlen = (high<<8)|low;
    if( (PACKHEADER_LEN+nrxdtlen) != nlen )
    {
        _up_station_receipt(SET_STATION_DAILYPARAMS,RPT_LENGTH,source);
        return false;
    }

    if(3 == nrxdtlen)
    {
        high = recdata[ii++];
        low = recdata[ii++];
        g_heart_interval = ((high<<8)|low);
        g_heart_state = recdata[ii++];
        err = _write_flash_alldata( );
        bret = _up_station_receipt(SET_STATION_HEARTPARAMS,err,source);
        return bret;
    }
    else
    {
        bret = _up_station_receipt(SET_STATION_HEARTPARAMS,RPT_FAILED,source);
        return false;
    }

    return bret;
}


//[下行]设置更新基站固件--暂时不支持
bool _set_station_update_firmware( unsigned char* recdata, unsigned short index, unsigned short nlen ,char source)
{
    bool bret  = true;
    if( (FRAME_LEN!=index)||((PACKHEADER_LEN+1)!=nlen) )
    {
        _up_station_receipt(SET_STATION_UPDATEFIRMWARE,RPT_RECLEN,source);
        return false;
    }

    int  ii = index;
    unsigned char high = recdata[ii++];
    unsigned char low = recdata[ii++];
    int nrxdtlen = (high<<8)|low;
    if( (PACKHEADER_LEN+nrxdtlen) != nlen )
    {
        _up_station_receipt(SET_STATION_UPDATEFIRMWARE,RPT_LENGTH,source);
        return false;
    }

    if(1 == nrxdtlen )
    {
        //暂不支持
        g_update_state = recdata[ii++];
        bret = _up_station_receipt(SET_STATION_UPDATEFIRMWARE,RPT_SUCCESS,source);
        return bret;
    }
    else
    {
        bret = _up_station_receipt(SET_STATION_UPDATEFIRMWARE,RPT_FAILED,source);
        return false;
    }

    return false;
}


//[下行]设置节能模块
bool _set_station_economizen( unsigned char* recdata, unsigned short index, unsigned short nlen ,char source)
{
    bool bret  = true;
    if( (FRAME_LEN!=index)||((PACKHEADER_LEN+1)!=nlen) )
    {
        _up_station_receipt(SET_STATION_ECONOMIZEN,RPT_RECLEN,source);
        return false;
    }

    char err =RPT_SUCCESS;
    int  ii = index;
    unsigned char high = recdata[ii++];
    unsigned char low = recdata[ii++];
    int nrxdtlen = (high<<8)|low;
    if( (PACKHEADER_LEN+nrxdtlen) != nlen )
    {
        _up_station_receipt(SET_STATION_ECONOMIZEN,RPT_LENGTH,source);
        return false;
    }
    
    if(1 == nrxdtlen )
    {
        g_economizen = recdata[ii];
        err = _write_flash_alldata( );
        bret = _up_station_receipt(SET_STATION_ECONOMIZEN,err,source);
        return bret;
    }
    else
    {
        bret = _up_station_receipt(SET_STATION_ECONOMIZEN,RPT_FAILED,source);
        return false;
    }

    return false;
}

//[下行]设置蜂鸣器响声
bool _set_station_beepon( unsigned char* recdata, unsigned short index, unsigned short nlen ,char source)
{
    bool bret  = true;
    if( (FRAME_LEN!=index)||((PACKHEADER_LEN+1)!=nlen) )
    {
        _up_station_receipt(SET_STATION_BEEPON,RPT_RECLEN,source);
        return false;
    }
    unsigned char err = RPT_SUCCESS;
    
    int  ii = index;
    unsigned char high = recdata[ii++];
    unsigned char low = recdata[ii++];
    int nrxdtlen = (high<<8)|low;
    if( (PACKHEADER_LEN+nrxdtlen) != nlen )
    {
        _up_station_receipt(SET_STATION_BEEPON,RPT_LENGTH,source);
        return false;
    }
    
    if(1 == nrxdtlen )
    {
        g_beep_state = recdata[ii];
        err = _write_flash_alldata( );
        bret = _up_station_receipt(SET_STATION_BEEPON,err,source);
        return bret;
    }
    else
    {
        err = RPT_FAILED;
        bret = _up_station_receipt(SET_STATION_BEEPON,err,source);
        return false;
    }

    return false;
}

//[下行]设置集中扫描参数
bool _set_station_focusparams( unsigned char* recdata, unsigned short index, unsigned short nlen ,char source)
{
    bool bret  = true;
    if( (FRAME_LEN!=index)||((PACKHEADER_LEN+4)!=nlen) )
    {
        _up_station_receipt(SET_STATION_FOCUSPARAMS,RPT_RECLEN,source);
        return false;
    }

    char err = RPT_SUCCESS;
    int  ii = index;
    unsigned char high = recdata[ii++];
    unsigned char low = recdata[ii++];
    int nrxdtlen = (high<<8)|low;
    if( (PACKHEADER_LEN+nrxdtlen) != nlen )
    {
        _up_station_receipt(SET_STATION_FOCUSPARAMS,RPT_LENGTH,source);
        return false;
    }

    if( 4 == nrxdtlen )
    {
        high = recdata[ii++];
        low = recdata[ii++];
        g_focus_interval = ((high<<8)|low);
        if(g_focus_interval<=7)
        {
            g_focus_interval = 8;
        }
        high = recdata[ii++];
        low = recdata[ii++];
        g_focus_scantime = ((high<<8)|low);
        err = _write_flash_alldata( );
        bret = _up_station_receipt(SET_STATION_FOCUSPARAMS,err,source);
        return bret;
    }
    else
    {
        bret = _up_station_receipt(SET_STATION_FOCUSPARAMS,RPT_FAILED,source);
        return false;
    }
    return false;
}


//[下行]设置读卡器的自动空闲状态
bool _set_station_idlestate( unsigned char* recdata, unsigned short index, unsigned short nlen ,char source)
{
    bool bret  = true;
    if( (FRAME_LEN!=index)||((PACKHEADER_LEN+4)!=nlen) )
    {
        _up_station_receipt(SET_STATION_IDLESTATE,RPT_RECLEN,source);
        return false;
    }
    
    unsigned char err = RPT_SUCCESS;
    int ii = index;
    unsigned char high = recdata[ii++];
    unsigned char low = recdata[ii++];
    int nrxdtlen = (high<<8)|low;
    if( (PACKHEADER_LEN+nrxdtlen) != nlen )
    {
        _up_station_receipt(SET_STATION_IDLESTATE,RPT_LENGTH,source);
        return false;
    }

    if( 4 == nrxdtlen)
    {
        g_idle_state = recdata[ii++];
        high = recdata[ii++];
        low = recdata[ii++];
        g_repeat_filter_time = ((high<<8)|low);
        g_threshold = recdata[ii++];
        bret = _up_station_receipt(SET_STATION_IDLESTATE,RPT_SUCCESS,source); 
        return bret;
    }
    else
    {
        bret = _up_station_receipt(SET_STATION_IDLESTATE,RPT_LENGTH,source); 
        return false;
    }
    
    return false;  
}

//[下行]查询心跳参数
bool _get_station_heartparams( unsigned char* recdata, unsigned short index, unsigned short nlen ,char source)
{
    bool bret  = true;
    if( (FRAME_LEN!=index)||(PACKHEADER_LEN!=nlen) )
    {
        _up_station_receipt(GET_STATION_HEARTPARAMS,RPT_RECLEN,source);
        return false;
    }
    
    unsigned char err = RPT_SUCCESS;
    int i = index;
    unsigned char high = recdata[i++];
    unsigned char low = recdata[i++];
    int nrxdtlen = (high<<8)|low;
    if( 0 == nrxdtlen)
    {
        int ii = 0;
	    unsigned char txbuf[20];
        memset(txbuf,0,20);
        txbuf[ii++] = STATION_FRAME_HEADER;
        txbuf[ii++] = STATION_MY_CMD;
        txbuf[ii++] = GET_STATION_HEARTPARAMS;
        txbuf[ii++] = 0x00;
        txbuf[ii++] = 0x03;
        txbuf[ii++] = g_heart_interval/256;
        txbuf[ii++] = g_heart_interval%256;
        txbuf[ii++] = g_heart_state;


        //回复基站管理的标签数据
        bret = _reply_data(txbuf,ii , GET_STATION_HEARTPARAMS,source);
        return bret;
    }
    else
    {
        bret = _up_station_receipt(GET_STATION_HEARTPARAMS,RPT_LENGTH,source); 
        return false;
    }
    return false;
}


//[下行]查询日常扫描参数
bool _get_station_dailyparams( unsigned char* recdata, unsigned short index, unsigned short nlen ,char source)
{
    bool bret  = true;
    if( (FRAME_LEN!=index)||(PACKHEADER_LEN!=nlen) )
    {
        _up_station_receipt(GET_STATION_DAILYPARAMS,RPT_RECLEN,source);
        return false;
    }
    
    // unsigned char err = RPT_SUCCESS; // 未使用，注释掉
    int i = index;
    unsigned char high = recdata[i++];
    unsigned char low = recdata[i++];
    int nrxdtlen = (high<<8)|low;
    if( 0 == nrxdtlen)
    {
        //FEEFC0000C003C000F010204010014010600
        int ii = 0;
	    unsigned char txbuf[20];
        memset(txbuf,0,20);
        txbuf[ii++] = STATION_FRAME_HEADER;
        txbuf[ii++] = STATION_MY_CMD;
        txbuf[ii++] = GET_STATION_DAILYPARAMS;
        txbuf[ii++] = 0x00;
        txbuf[ii++] = 0x0c;
        txbuf[ii++] = g_daily_interval/256;
        txbuf[ii++] = g_daily_interval%256;
        txbuf[ii++] = g_daily_scantime/256;    
        txbuf[ii++] = g_daily_scantime%256;
        txbuf[ii++] = g_daily_is_working;
        txbuf[ii++] = g_daily_scaning_state;
        txbuf[ii++] = g_daily_rec_state;
        txbuf[ii++] = g_scan_type;
        txbuf[ii++] = g_repeat_filter_time/256;
        txbuf[ii++] = g_repeat_filter_time%256;
        txbuf[ii++] = g_daily_scan_state;
        txbuf[ii++] = g_daily_scan_way;    

        //回复日常扫描参数数据
        bret = _reply_data(txbuf,ii , GET_STATION_DAILYPARAMS,source);
        return bret;
    }
    else
    {
        bret = _up_station_receipt(GET_STATION_DAILYPARAMS,RPT_LENGTH,source); 
        return false;
    }
    return false;
}


//[下行]查询集中扫描参数
bool _get_station_focusparams( unsigned char* recdata, unsigned short index, unsigned short nlen ,char source)
{
    bool bret  = true;
    if( (FRAME_LEN!=index)||(PACKHEADER_LEN!=nlen) )
    {
        _up_station_receipt(GET_STATION_FOCUSPARAMS,RPT_RECLEN,source);
        return false;
    }

    // unsigned char err = RPT_SUCCESS; // 未使用，注释掉
    int i = index;
    unsigned char high = recdata[i++];
    unsigned char low = recdata[i++];
    int nrxdtlen = (high<<8)|low;
    if( 0 == nrxdtlen)
    {
        bool bret = true;
        int ii = 0;
	    unsigned char txbuf[20];
        memset(txbuf,0,20);
        txbuf[ii++] = STATION_FRAME_HEADER;
        txbuf[ii++] = STATION_MY_CMD;
        txbuf[ii++] = GET_STATION_FOCUSPARAMS;
        txbuf[ii++] = 0x00;
        txbuf[ii++] = 0x0a;
        txbuf[ii++] = g_focus_interval/256;
        txbuf[ii++] = g_focus_interval%256;
        txbuf[ii++] = g_focus_scantime/256;    
        txbuf[ii++] = g_focus_scantime%256;
        txbuf[ii++] = g_focus_is_working;
        txbuf[ii++] = g_focus_scaning_state;
        txbuf[ii++] = g_focus_rec_state;
        txbuf[ii++] = g_scan_type;
        txbuf[ii++] = g_repeat_filter_time/256;
        txbuf[ii++] = g_repeat_filter_time%256;

        //回复集中扫描参数数据
        bret = _reply_data(txbuf,ii , GET_STATION_FOCUSPARAMS,source);
        return bret;
    }
    else
    {
        bret = _up_station_receipt(GET_STATION_FOCUSPARAMS,RPT_LENGTH,source); 
        return false;
    }
    return false;
}


//[下行]设置读卡器的空闲时间/扫描时间
bool _set_station_idlescan( unsigned char* recdata, unsigned short index, unsigned short nlen ,char source)
{
    bool bret  = true;
    if( (FRAME_LEN!=index)||((PACKHEADER_LEN+4)!=nlen) )
    {
        _up_station_receipt(SET_STATION_IDLESCAN,RPT_RECLEN,source);
        return false;
    }

    unsigned char err = RPT_SUCCESS;
    int i = index;
    unsigned char high = recdata[i++];
    unsigned char low = recdata[i++];
    int nrxdtlen = (high<<8)|low;
    if( (PACKHEADER_LEN+nrxdtlen) != nlen )
    {
        _up_station_receipt(SET_STATION_IDLESCAN,RPT_LENGTH,source);
        return false;
    }

    if( 4 == nrxdtlen)
    {
        high = recdata[i++];
        low = recdata[i++];
        g_idle_time = ((high<<8)|low);
        high = recdata[i++];
        low = recdata[i++];
        g_scan_time = ((high<<8)|low);
        err = _write_flash_alldata();
        
        bret = _up_station_receipt(SET_STATION_IDLESCAN,RPT_SUCCESS,source);  
        return bret; 
    }
    else
    {
        bret = _up_station_receipt(SET_STATION_IDLESCAN,RPT_LENGTH,source);   
        return false;
    }
    return false;
}


//[下行]查询基站版本
bool _get_station_soft_ver( unsigned char* recdata, unsigned short index, unsigned short nlen ,char source)
{
    bool bret  = true;
    if( (FRAME_LEN!=index)||(PACKHEADER_LEN!=nlen) )
    {
        _up_station_receipt(GET_STATION_SOFT_VER,RPT_RECLEN,source);
        return false;
    }
    
    // unsigned char err = RPT_SUCCESS; // 未使用，注释掉
    int i = index;
    unsigned char high = recdata[i++];
    unsigned char low = recdata[i++];
    int nrxdtlen = (high<<8)|low;
    if( 0 == nrxdtlen)
    {
        int ii = 0;
	    unsigned char txbuf[12];
        memset(txbuf,0,12);
        txbuf[ii++] = STATION_FRAME_HEADER;
        txbuf[ii++] = STATION_MY_CMD;
        txbuf[ii++] = GET_STATION_SOFT_VER;
        txbuf[ii++] = 0x00;
        txbuf[ii++] = 0x04;
        txbuf[ii++] = g_hardware_version;
        txbuf[ii++] = g_firmware_version;
        txbuf[ii++] = g_protocol_version;
        txbuf[ii++] = g_reader_ver;
        //回复基站版本数据
        bret = _reply_data(txbuf,ii , GET_STATION_SOFT_VER,source);
        return bret;
    }
    else
    {
        bret = _up_station_receipt(GET_STATION_SOFT_VER,RPT_LENGTH,source); 
        return false;
    }
    return false;	
}

//[下行]设置基站的透传开关
bool _set_station_tt_state( unsigned char* recdata, unsigned short index, unsigned short nlen ,char source)
{
    bool bret  = true;
    if( (FRAME_LEN!=index)||((PACKHEADER_LEN+1)!=nlen) )
    {
        _up_station_receipt(SET_STATION_TT_STATE,RPT_RECLEN,source);
        return false;
    }
    
    unsigned char err = RPT_SUCCESS;
    int ii = index;
    unsigned char high = recdata[ii++];
    unsigned char low = recdata[ii++];
    int nrxdtlen = (high<<8)|low;
    if( (PACKHEADER_LEN+nrxdtlen) != nlen )
    {
        _up_station_receipt(SET_STATION_TT_STATE,RPT_LENGTH,source);
        return false;
    }

    if( 1 == nrxdtlen)
    {        
        g_transparent_transmission = recdata[ii++];
        err = _write_flash_alldata(); 
        bret = _up_station_receipt(SET_STATION_TT_STATE,err,source);  
        return bret;  
    }
    else
    {
        bret = _up_station_receipt(SET_STATION_TT_STATE,RPT_LENGTH,source);    
        return false;
    }
    return false;
}

//[下行]查询基站的透传开关
bool _get_station_tt_state( unsigned char* recdata, unsigned short index, unsigned short nlen ,char source)
{
    bool bret  = true;
    if( (FRAME_LEN!=index)||(PACKHEADER_LEN!=nlen) )
    {
        _up_station_receipt(GET_STATION_TT_STATE,RPT_RECLEN,source);
        return false;
    }
    
    unsigned char err = RPT_SUCCESS;
    int i = index;
    unsigned char high = recdata[i++];
    unsigned char low = recdata[i++];
    int nrxdtlen = (high<<8)|low;
    if( 0 == nrxdtlen)
    {    
        int ii = 0;
	    unsigned char txbuf[10];
        memset(txbuf,0,10);
        txbuf[ii++] = STATION_FRAME_HEADER;
        txbuf[ii++] = STATION_MY_CMD;
        txbuf[ii++] = GET_STATION_TT_STATE;
        txbuf[ii++] = 0x00;
        txbuf[ii++] = 0x01;
        txbuf[ii++] = g_transparent_transmission;

        //回复基站的透传开关
        bret = _reply_data(txbuf,ii , GET_STATION_TT_STATE,source);
        return bret;
    }
    else
    {
        bret = _up_station_receipt(GET_STATION_TT_STATE,RPT_LENGTH,source); 
        return false;
    }
    return false;
}

//[下行]设置基站擦除数据
bool _set_station_earse_params( unsigned char* recdata, unsigned short index, unsigned short nlen,char source )
{
    bool bret  = true;
    if( (FRAME_LEN!=index)||(PACKHEADER_LEN!=nlen) )
    {
        _up_station_receipt(SET_STATION_EARSE_PARAMS,RPT_RECLEN,source);
        return false;
    }

    unsigned char err = RPT_SUCCESS;
    int ii = index;
    unsigned char high = recdata[ii++];
    unsigned char low = recdata[ii++];
    int nrxdtlen = (high<<8)|low;
    if(0 == nrxdtlen)
    {
        if(IS_SOCKET & source)
        {
            //不支持
            ESP_ERROR_CHECK(nvs_flash_erase());
            esp_err_t err_t = nvs_flash_init();
            bret = _up_station_receipt(SET_STATION_EARSE_PARAMS,RPT_SUCCESS,source);
            return bret;
        }
        else if(IS_SERIAL & source)
        {
            ESP_ERROR_CHECK(nvs_flash_erase());
            esp_err_t err_t = nvs_flash_init();
            bret = _up_station_receipt(SET_STATION_EARSE_PARAMS,RPT_SUCCESS,source);
            return bret;
        }
        else
        {
            bret = _up_station_receipt(SET_STATION_EARSE_PARAMS,RPT_FAILED,source);
            return false;
        }
    }
    else
    {
        bret = _up_station_receipt(SET_STATION_EARSE_PARAMS,RPT_LENGTH,source);
        return false;
    }
    return false;
}

//[下行]设置WIFI和服务器的参数
bool _set_station_netparams( unsigned char* recdata, unsigned short index, unsigned short nlen ,char source)
{   
    bool bret  = true;
    if( (FRAME_LEN!=index)||((PACKHEADER_LEN+IPADDRESS_LEN+10)>=nlen) )
    {
        _up_station_receipt(SET_STATION_NETPARAMS,RPT_RECLEN,source);
        return false;
    }
    
    unsigned char err = RPT_SUCCESS;
    int ii = index+2;
    unsigned char high = recdata[ii++];
    unsigned char low = recdata[ii++];
    int nrxdtlen = (high<<8)|low;
    
    if(IS_SOCKET & source)
    {
        if( ((PACKHEADER_LEN+nrxdtlen) != nlen ))
        {
            _up_station_receipt(SET_STATION_NETPARAMS,RPT_LENGTH,source);
            return false;
        }
        if( (PACKHEADER_LEN+nrxdtlen) >= RX1_BUF_SIZE )
        {
            _up_station_receipt(SET_STATION_NETPARAMS,RPT_PACKLEN,source);
            return false;
        }
    
        unsigned char nssid = 0xFF&recdata[ii++];
        if( (nssid > SSID_LEN)||(nssid+IPADDRESS_LEN+PACKHEADER_LEN+10)>nlen )
        {
            _up_station_receipt(SET_STATION_NETPARAMS,RPT_PACKLEN,source);
            return false;
        }
        for(int i=0;i<nssid;i++)
        {
            g_swifi_ssid[i] = recdata[ii++];
        }
        unsigned char npwsd = 0xFF&recdata[ii++];
        if( (npwsd > PSWD_LEN)||(nssid+npwsd+IPADDRESS_LEN+PACKHEADER_LEN+1)>nlen )
        {
            _up_station_receipt(SET_STATION_NETPARAMS,RPT_PACKLEN,source);
            return false;
        }
        for(int i=0;i<npwsd;i++)
        {
            g_swifi_password[i] = recdata[ii++];
        }
        unsigned char niplen = 0xFF&recdata[ii++];
        if( (niplen >= IPADDRESS_LEN)||(nssid+npwsd+niplen+PACKHEADER_LEN+2)>nlen )
        {
            _up_station_receipt(SET_STATION_NETPARAMS,RPT_PACKLEN,source);
            return false;
        }
        for(int i=0;i<niplen;i++)
        {
            g_sserver_ip[i] = recdata[ii++];
        }
        unsigned char  high = recdata[ii++];
        unsigned char  low =  recdata[ii++];
        g_server_port = (high<<8)|low;
        
        err = _write_flash_netparams();
        bret = _up_station_receipt(SET_STATION_NETPARAMS,err,source);    
        return bret;
    }
    else if(IS_SERIAL & source)
    {
        //#{BY-WIFI}#by88888888#{**************}#8160#
        // unsigned char high = recdata[index]; // 未使用，注释掉
        // unsigned char low = recdata[index+1]; // 未使用，注释掉
        // unsigned short ndatalen = (((high<<8)|low)); // 未使用，注释掉

        int k = PACKHEADER_LEN;
        // char strport[10]; // 未使用，注释掉
        if ((recdata[k] != '#') && (recdata[k+1] != '{'))
        {
            bret = _up_station_receipt(SET_STATION_NETPARAMS,RPT_PACKAGE,source);
            return false;
        } 
        for (int i = 0;i < SSID_LEN;i++)//wifi_ssid
        {
            g_swifi_ssid[i] = recdata[i+k + 2];
            if ((recdata[i+k + 3] == '}' )&& (recdata[i+k + 4] == '#'))
            {
                g_swifi_ssid[i + 1] = '\0';
                k = k+i + 5;
                break;
            }
        }
        for (int m = 0;m < PSWD_LEN;m++)//wifi_password
        {
            g_swifi_password[m] = recdata[k];
            if (recdata[k + 1] == '#')
            {
                g_swifi_password[m + 1] = '\0';
                break;
            }
            k++;
        }

        for (int i = 0;i < IPADDRESS_LEN;i++)//TCP_SERVER_ADRESS
        {
            g_sserver_ip[i] = recdata[i + k + 3];
            if( (recdata[i + k + 4] == '}' )&& (recdata[i + k + 5] == '#') )
            {
                g_sserver_ip[i + 1] = '\0';
                k = i + k + 5 + 1;
                break;
            }
        }
        for (int m = 0;m < 32;m++)//TCP_SERVER_PORT
        {
            char str[10];
            str[m] = recdata[k];
            if (recdata[k+1] == '#')
            {
                str[m + 1] = '\0';
                g_server_port = atoi(str);//转为整数
                break;
            }
            k++;
        }
        err = _write_flash_netparams();
        bret = _up_station_receipt(SET_STATION_NETPARAMS,err,source); 
        return bret;
    }
    else
    {
        bret = _up_station_receipt(SET_STATION_NETPARAMS,RPT_FAILED,source);   
        return false;
    }
    return false;
}


//[下行]查询WIFI和服务器的参数
bool _get_station_netparams( unsigned char* recdata, unsigned short index, unsigned short nlen,char source )
{   
    bool bret  = true;
    if( (FRAME_LEN!=index)||(PACKHEADER_LEN!=nlen) )
    {
        _up_station_receipt(GET_STATION_NETPARAMS,RPT_RECLEN,source);
        return false;
    }
    
    esp_err_t err = 0;
    int ii = index;
    unsigned char high = recdata[ii++];
    unsigned char low = recdata[ii++];
    int nrxdtlen = (high<<8)|low;
    
    if(IS_SOCKET == source)
    { 
        if(0 != nrxdtlen)
        {
            _up_station_receipt(GET_STATION_NETPARAMS,RPT_LENGTH,source);
            return false;
        }
        int ii = 0;
        unsigned char txbuf[150];
        memset(txbuf,0,150);
        txbuf[ii++] = STATION_FRAME_HEADER;
        txbuf[ii++] = STATION_MY_CMD;
        txbuf[ii++] = GET_STATION_NETPARAMS;
        ii = ii+2;
	    int nssid = strlen(g_swifi_ssid);
        txbuf[ii++] = 0xFF&nssid;
        for(int i=0;i<nssid;i++)
        {
            txbuf[ii++] = g_swifi_ssid[i];
        }
        int npwsd = strlen(g_swifi_password);
        txbuf[ii++] =  0xFF&npwsd;
        for(int j=0;j<npwsd;j++)
        {
            txbuf[ii++] = g_swifi_password[j];
        }
        txbuf[ii++] =  0xFF&IPADDRESS_LEN;
        for(int k=0;k<IPADDRESS_LEN;k++)
        {
            txbuf[ii++] = g_sserver_ip[k];
        }
        txbuf[ii++] = g_server_port/256;
        txbuf[ii++] = g_server_port%256;

        txbuf[3] = (ii-PACKHEADER_LEN)/256;
        txbuf[4] = (ii-PACKHEADER_LEN)%256;

        bret = _reply_data(txbuf,ii , GET_STATION_NETPARAMS,source);
        return bret;
    }
    else if(IS_SERIAL == source)
    {
        unsigned char txbuf[200];
        memset(txbuf,0,200);
        sprintf((char*)txbuf, "\r\nwifi_ssid : %s\r\twifi_password : %s\r\tserver_ip : %s\r\tserver_port : %d\r\n",
            g_swifi_ssid,g_swifi_password,g_sserver_ip,g_server_port);

        err = uart_write_bytes(UART_NUM_1, txbuf, strlen((char*)txbuf));
        return true;
    }
    else
    {
        bret = _up_station_receipt(GET_STATION_NETPARAMS,RPT_LENGTH,source); 
        return false;
    }
    return false;
}

//[下行]设置设备ID
bool _set_station_deviceid( unsigned char* recdata, unsigned short index, unsigned short nlen,char source )
{  
    bool bret  = true;
    if( (FRAME_LEN!=index)||((PACKHEADER_LEN+4)!=nlen) )
    {
        _up_station_receipt(SET_STATION_DEVICEID,RPT_RECLEN,source);
        return false;
    }
    if( (index+2)  > nlen )
    {
        bret = false;
        _up_station_receipt(SET_STATION_DEVICEID,RPT_LENGTH,source);
        return bret;
    }  
    unsigned char err = RPT_SUCCESS;
    
    int ii = index;
    unsigned char high = recdata[ii++];
    unsigned char low = recdata[ii++];
    int nrxdtlen = (high<<8)|low;
    if( ((PACKHEADER_LEN+nrxdtlen) != nlen ))
    {
        _up_station_receipt(SET_STATION_DEVICEID,RPT_LENGTH,source);
        return false;
    }
    if( (PACKHEADER_LEN+nrxdtlen) >= RX1_BUF_SIZE )
    {
        _up_station_receipt(SET_STATION_DEVICEID,RPT_PACKLEN,source);
        return false;
    }
    
    if((DEVID_LEN-1) == nrxdtlen)
    {
        for(int i=0;i<nrxdtlen;i++)
        {
            g_device_id[i] = recdata[ii++];
        }        
        g_device_id[DEVID_LEN-1] = '\0';
        if(IS_SOCKET & source)
        {            
            //不开放该功能
            err = _write_flash_deviceid();
            //err = RPT_NOSUP;
            bret = _up_station_receipt(SET_STATION_DEVICEID,err,source);
            return true;
        }
        else if(IS_SERIAL & source)
        {
            err = _write_flash_deviceid();
            bret = _up_station_receipt(SET_STATION_DEVICEID,err,source);
            return true;
        }
        else
        {
            bret = _up_station_receipt(SET_STATION_DEVICEID,RPT_FAILED,source);
            return false;
        }            
    }
    else
    {
        bret = _up_station_receipt(SET_STATION_DEVICEID,RPT_LENGTH,source);
        return false;
    } 
    return false;
}

//[下行]查询设备ID
bool _get_station_deviceid( unsigned char* recdata, unsigned short index, unsigned short nlen,char source )
{  
    bool bret  = true;
    if( (FRAME_LEN!=index)||(PACKHEADER_LEN!=nlen) )
    {
        _up_station_receipt(GET_STATION_DEVICEID,RPT_RECLEN,source);
        return false;
    }
    int kk = index;
    unsigned char high = recdata[kk++];
    unsigned char low = recdata[kk++];
    int nrxdtlen = (high<<8)|low;
     
    if(0 == nrxdtlen)
    {
        int ii = 0;
	    unsigned char txbuf[20];
        memset(txbuf,0,20);
        txbuf[ii++] = STATION_FRAME_HEADER;
        txbuf[ii++] = STATION_MY_CMD;
        txbuf[ii++] = GET_STATION_DEVICEID;
        txbuf[ii++] = 0x00;
        txbuf[ii++] = 0x04;
        for(int i=0;i<(DEVID_LEN-1);i++)
        {
            txbuf[ii++] = g_device_id[i];
        }

        bret = _reply_data(txbuf,ii , GET_STATION_DEVICEID,source);
        return bret;
    }
    else
    {
        bret = _up_station_receipt(GET_STATION_DEVICEID,RPT_LENGTH,source); 
        return false;
    }
    return false;
}

//[下行]设置为出厂默认设置
bool _up_station_restore_setting( unsigned char* recdata, unsigned short index, unsigned short nlen ,char source)
{
    bool bret = false;    
    if( (FRAME_LEN!=index)||(PACKHEADER_LEN!=nlen) )
    {
        _up_station_receipt(UP_STATION_RESTORE_SETTING,RPT_RECLEN,source);
        return false;
    }
    
    char err = RPT_SUCCESS;
    int ii = index;
    unsigned char high = recdata[ii++];
    unsigned char low = recdata[ii++];
    int ndatalen = (high<<8)|low;
    if( 0 != ndatalen )
    {
        bret = false;
        _up_station_receipt(UP_STATION_RESTORE_SETTING,RPT_LENGTH,source);
        return false;   
    }
    else
    {
        if(IS_SOCKET & source)
        {            
            //不开放该功能
            //init run temp data
            char err = RPT_OTHER;
            _init_station_data();
            _init_run_data();
            err = _write_flash_alldata();
            bret = _up_station_receipt(UP_STATION_RESTORE_SETTING,err,source);    
            return bret;  
        }
        else if(IS_SERIAL & source)
        {
            //init run temp data
            _init_station_data();
            _init_run_data();
            char err = _write_flash_alldata();
            bret = _up_station_receipt(UP_STATION_RESTORE_SETTING,err,source);    
            return bret;  
        }
        else
        {
            bret = _up_station_receipt(UP_STATION_RESTORE_SETTING,RPT_FAILED,source);
            return bret; 
        }        
    }
    return false;
}

//[上行]心跳
bool _up_station_heart( unsigned char source)
{
    g_heart_intervaling = 0;  
    if((enDONING == g_focus_is_working))
    {
        //ESP_LOGI(TAG, "[心跳发送] 正在进行集中扫描，跳过心跳发送");
        return false;
    }
    
    // 检查连接状态
    if(g_online_state == enNO)
    {
        //ESP_LOGW(TAG, "[心跳发送] 设备离线，无法发送心跳");
        return false;
    }
    
    g_heart_is_send = enDONING;
    //ESP_LOGI(TAG, "[心跳发送] 开始发送心跳数据");
    
    //FEEFB000145A5A5A5A00001401003C000F001E0014060201
    int ii = 0;
    memset(g_heartdata, 0, MAX_HEART_LEN);    
    g_heartdata[ii++] = STATION_FRAME_HEADER;
    g_heartdata[ii++] = STATION_MY_CMD;
    g_heartdata[ii++] = UP_STATION_HEART;
    ii = ii + 2;
    for(int j=0;j<(DEVID_LEN-1);j++)
    {
        g_heartdata[ii++] = g_device_id[j];
    }
    g_heartdata[ii++] = 0xFF&g_down_count;
    g_heartdata[ii++] = (g_heart_interval)/256;
    g_heartdata[ii++] = (g_heart_interval)%256;
    g_heartdata[ii++] = g_heart_state;
    g_heartdata[ii++] = (g_daily_interval)/256;
    g_heartdata[ii++] = (g_daily_interval)%256;
    g_heartdata[ii++] = (g_daily_scantime)/256;
    g_heartdata[ii++] = (g_daily_scantime)%256;
    g_heartdata[ii++] = (g_focus_interval)/256;
    g_heartdata[ii++] = (g_focus_interval)%256;
    g_heartdata[ii++] = (g_focus_scantime)/256;
    g_heartdata[ii++] = (g_focus_scantime)%256;
    g_heartdata[ii++] = g_daily_scan_way;
    g_heartdata[ii++] = g_transparent_transmission;
    g_heartdata[ii++] = g_daily_scan_state;
    g_heartdata[3] = (ii-PACKHEADER_LEN)/256;
    g_heartdata[4] = (ii-PACKHEADER_LEN)%256;

    int ret = send(g_connect_socket, g_heartdata, ii, 0);
    if(ret < 0)
    {
        int error_no = errno;
        //ESP_LOGE(TAG, "[心跳发送] 心跳发送失败，错误码: %d (%s)", error_no, strerror(error_no));
        
        // 只有在严重网络错误时才触发重连，避免临时性问题导致的重连
        if (error_no == ECONNRESET || error_no == EPIPE || error_no == ENOTCONN || error_no == ECONNABORTED)
        {
            //ESP_LOGE(TAG, "[心跳发送] 检测到连接断开，触发重连");
            g_online_state = enNO;
            g_rxtx_need_restart = true;
        }
        else
        {
            //ESP_LOGW(TAG, "[心跳发送] 临时性发送错误，不触发重连");
        }
        
        g_heart_is_send = enCOMPLETE;
        return false;
    }
    else if(ret != ii)
    {
        //ESP_LOGW(TAG, "[心跳发送] 心跳部分发送，期望: %d字节, 实际: %d字节", ii, ret);
        g_heart_is_send = enCOMPLETE;
        return false;
    }
    else
    {
        //ESP_LOGI(TAG, "[心跳发送] 心跳发送成功，发送字节数: %d", ret);
        g_online_state = enYES;
        g_heart_is_send = enCOMPLETE;
    }

    return true;
}
//向终端发送数据
bool _reply_data(unsigned char* txbuf,unsigned int ntx,char cmd,char source)
{
    bool bret = false;
    if(IS_SOCKET == source)
    {
        // 检查连接状态
        if(g_online_state == enNO)
        {
            //ESP_LOGW(TAG, "[数据发送] 设备离线，无法发送数据，命令: 0x%02X", cmd);
            return false;
        }
        
        int ret = send(g_connect_socket, txbuf, ntx, 0);
        if(ret < 0)
        {
            int error_no = errno;
            //ESP_LOGE(TAG, "[数据发送] 数据发送失败，命令: 0x%02X, 错误码: %d (%s)", cmd, error_no, strerror(error_no));
            
            // 只有在严重网络错误时才触发重连
            if (error_no == ECONNRESET || error_no == EPIPE || error_no == ENOTCONN || error_no == ECONNABORTED)
            {
                g_online_state = enNO;        // 发送失败，判定离线
                g_rxtx_need_restart = true;   // 触发重连
                //ESP_LOGI(TAG, "[数据发送] 检测到连接断开，已触发重连机制");
            }
            else
            {
                //ESP_LOGW(TAG, "[数据发送] 临时性发送错误，不触发重连");
            }
            bret = false;
        }
        else
        {
            //ESP_LOGI(TAG, "[数据发送] 数据发送成功，命令: 0x%02X, 发送字节数: %d", cmd, ret);
            g_online_state = enYES;
            bret = true;
        }
    }
    if(IS_SERIAL == source)
    {
        bret = uart_write_bytes(UART_NUM_1, txbuf, ntx );
        bret = true;
    }
    if(!bret)
    {
        _up_station_receipt(cmd,RPT_FAILED,source);
    }
    return bret;
}
//[上行]发送错误代码
bool _up_station_errcode(unsigned char cmd,unsigned char code,char source)
{
    bool bret = false;
    unsigned char txbuf[20];
    memset(txbuf,0,20);
    int ii=0;

    txbuf[ii++] = STATION_FRAME_HEADER;
    txbuf[ii++] = STATION_MY_CMD;
    txbuf[ii++] = UP_STATION_ERROR;
    txbuf[ii++] = 0x00;
    txbuf[ii++] = 0x02;
    txbuf[ii++] = cmd;
    txbuf[ii++] = code;
 
    if( IS_SOCKET&source )
    {
        // 检查连接状态
        if(g_online_state == enNO)
        {
            //ESP_LOGW(TAG, "[错误上报] 设备离线，无法发送错误代码，命令: 0x%02X, 错误码: 0x%02X", cmd, code);
            return false;
        }
        
        int ret = send(g_connect_socket, txbuf, ii , 0);
        if(ret < 0)
        {
            //ESP_LOGE(TAG, "[错误上报] 错误代码发送失败，命令: 0x%02X, 错误码: 0x%02X, 发送错误: %d (%s)",cmd, code, errno, strerror(errno));
            g_online_state = enNO;
            g_rxtx_need_restart = true;
            //ESP_LOGI(TAG, "[错误上报] 已触发重连机制");
            bret = false;
        }
        else
        {
            //ESP_LOGI(TAG, "[错误上报] 错误代码发送成功，命令: 0x%02X, 错误码: 0x%02X", cmd, code);
            bret = true;
        }
    }
    if( IS_SERIAL&source)
    {
        esp_err_t err = uart_write_bytes(UART_NUM_1, txbuf, ii );
        bret = true;
    }
    return bret;

}

//[上行]发送包含代码的执行结果回执 
bool _up_station_receipt(unsigned char cmd,unsigned char code,char source)
{
    bool bret = false;
    unsigned char rpt[20];
    memset(rpt,0,20);
    int kk=0;
    rpt[kk++] = STATION_FRAME_HEADER;
    rpt[kk++] = STATION_MY_CMD;
    rpt[kk++] = cmd;
    rpt[kk++] = 0x00;
    rpt[kk++] = 0x01;
    rpt[kk++] = code;

    if( IS_SOCKET & source )
    {
        // 检查连接状态
        if(g_online_state == enNO)
        {
            //ESP_LOGW(TAG, "[回执发送] 设备离线，无法发送回执，命令: 0x%02X, 代码: 0x%02X", cmd, code);
            return false;
        }
        
        int ret = send(g_connect_socket, rpt, kk , 0);
        if(ret < 0)
        {
            //ESP_LOGE(TAG, "[回执发送] 回执发送失败，命令: 0x%02X, 代码: 0x%02X, 发送错误: %d (%s)",cmd, code, errno, strerror(errno));
            g_online_state = enNO;
            g_rxtx_need_restart = true;
            //ESP_LOGI(TAG, "[回执发送] 已触发重连机制");
            bret = false;
        }
        else
        {
            //ESP_LOGI(TAG, "[回执发送] 回执发送成功，命令: 0x%02X, 代码: 0x%02X", cmd, code);
            bret = true;
        }
    }
    if( IS_SERIAL & source)
    {
        esp_err_t err = uart_write_bytes(UART_NUM_1, rpt, kk );
        bret = true;
    }

    return bret;
}

//[测试]上报基站发现的故障 
void _net_testout(  char *perr,char cmd, char code,char source)
{
    int errlen = strlen(perr);
    char *txbuf = ( char*)malloc(PACKHEADER_LEN+errlen+10);
    memset(txbuf,0,PACKHEADER_LEN+errlen+10);
    int kk=0;
    int ntxlen = PACKHEADER_LEN+errlen+sizeof(short);
    txbuf[kk++] = STATION_FRAME_HEADER;
    txbuf[kk++] = STATION_MY_CMD;
    txbuf[kk++] = cmd;
    // txbuf[kk++] = code;
    // char intc[5];
    // memset(intc,0,5);    
    // itoa(errlen,intc,4);
    // txbuf[kk++] = intc[0];
    // txbuf[kk++] = intc[1];
    // txbuf[kk++] = intc[2];
    // txbuf[kk++] = intc[3];

    for(int i=0;i<errlen;i++)
    {
        txbuf[kk++] = perr[i];
    }
    if( IS_SOCKET & source )
    {
        // 检查连接状态
        if(g_online_state == enNO)
        {
            //ESP_LOGW(TAG, "[测试输出] 设备离线，无法发送测试数据");
        }
        else
        {
            int ret = send(g_connect_socket, txbuf, kk , 0);
            if(ret < 0)
            {
                //ESP_LOGE(TAG, "[测试输出] 测试数据发送失败，错误: %d (%s)", errno, strerror(errno));
                g_online_state = enNO;
                g_rxtx_need_restart = true;
                //ESP_LOGI(TAG, "[测试输出] 已触发重连机制");
            }
            else
            {
                //ESP_LOGI(TAG, "[测试输出] 测试数据发送成功，发送字节数: %d", ret);
            }
        }
    }
    if( IS_SERIAL & source)
    {
        esp_err_t err = uart_write_bytes(UART_NUM_1, txbuf, kk );
    }
    if(NULL != txbuf)
        free(txbuf);
}


void _tag_dupli_removal(unsigned char scan_type)
{
    int nindex = PACKHEADER_LEN;
    //Check for repeatability
    // int t = 0; // 未使用，注释掉
    if(enDAILY==scan_type)
    {
        for (int n = 0;n < g_daily_count-1;n++)
        {
            int j = nindex +TAG_LEN*n;
            int m = g_daily_index-12;
            if((g_daily_tag[m++]==g_daily_tag[j++])&&
            (g_daily_tag[m++]==g_daily_tag[j++])&&
            (g_daily_tag[m++]==g_daily_tag[j++])&&
            (g_daily_tag[m++]==g_daily_tag[j++])&&
            (g_daily_tag[m++]==g_daily_tag[j++])&&
            (g_daily_tag[m++]==g_daily_tag[j++])&&
            (g_daily_tag[m++]==g_daily_tag[j++])&&
            (g_daily_tag[m++]==g_daily_tag[j++])&&
            (g_daily_tag[m++]==g_daily_tag[j++])&&
            (g_daily_tag[m++]==g_daily_tag[j++])&&
            (g_daily_tag[m++]==g_daily_tag[j++])&&
            (g_daily_tag[m++]==g_daily_tag[j++]))
            {
                g_daily_count--;
                g_daily_index = g_daily_index-12;
            }
        }
    }
    else if(enFOCUS ==scan_type)
    {
        for (int n = 0;n < g_focus_count-1;n++)
        {
            int j = nindex +TAG_LEN*n;
            int m = g_focus_index-12;
            if((g_focus_tag[m++]==g_focus_tag[j++])&&
            (g_focus_tag[m++]==g_focus_tag[j++])&&
            (g_focus_tag[m++]==g_focus_tag[j++])&&
            (g_focus_tag[m++]==g_focus_tag[j++])&&
            (g_focus_tag[m++]==g_focus_tag[j++])&&
            (g_focus_tag[m++]==g_focus_tag[j++])&&
            (g_focus_tag[m++]==g_focus_tag[j++])&&
            (g_focus_tag[m++]==g_focus_tag[j++])&&
            (g_focus_tag[m++]==g_focus_tag[j++])&&
            (g_focus_tag[m++]==g_focus_tag[j++])&&
            (g_focus_tag[m++]==g_focus_tag[j++])&&
            (g_focus_tag[m++]==g_focus_tag[j++]))
            {
                g_focus_count--;
                g_focus_index = g_focus_index-12;
            }
        }
    }
}
// void _tag_dupli_removal(unsigned char scan_type)
// {    
    // for (int n = 0;n < g_daily_count;n++)
    // {
    //     //Check for repeatability
    //     int t = 0;
    //     for(int i=0;i<TAG_LEN;i++)
    //     {   
    //         t = ii;
    //         if(g_daily_tag[PACKHEADER_LEN+g_daily_index++] == recdata[t++]) 
    //         {
    //             ntemp = 1;
    //         }
    //         else
    //         {
    //             ntemp = 0;
    //         }
    //         ncheck = ncheck|ntemp<<1;
    //     }
    // }
    // bcheck = ncheck&4095;
    // if(!bcheck)
    // {
    //     //check array overflow
    //     int nix = PACKHEADER_LEN+g_daily_count*TAG_LEN;

    //     if (g_daily_count+1 <= MAX_COUNT)
    //     { 
    //         for(int i=0;i<TAG_LEN;i++)
    //         {
    //            g_daily_tag[PACKHEADER_LEN+nix++] = recdata[ii++];
    //         }    
    //         g_daily_count++;
    //     }
    //     else
    //     {
    //         //接收缓冲区满，数据丢弃
    //     }
    //}
// }

//主线程中处理日常扫描的部分
void _main_daily_scan(char source)
{
    bool bret = true;
    if(enNO == g_daily_scan_state)
    {
        //_up_station_errcode(QUERY_TAG_CONTINUE,RPT_FAILED,IS_SOCKET);
        return;
    }
    if ((enIDLE == g_focus_is_working))
    {
        if ((g_daily_intervaling >= 0) && (g_daily_intervaling <= g_daily_scantime))
        {
            if (g_daily_rec_state != enDONING)
            {
                _begin_daily_scan();
            }
            else
            {
                g_daily_scaning_state = enDONING;
            }
        }
        else if ((g_daily_intervaling >= g_daily_scantime) && (g_daily_intervaling <= (g_daily_interval + g_daily_scantime)))
        {
            if (g_daily_rec_state != enCOMPLETE)
            {
                if (g_daily_is_working == enDONING)
                {
                    _end_daily_scan();
                }
                else
                {
                    g_daily_is_working = enIDLE;
                }
            }
            else
            {
                if (enDONING == g_daily_is_working)
                {
                    bret = _up_station_daily_tag(source);
                }
            }
        }
        else
        {
            g_daily_intervaling = 0;
        }
    }
    else
    {
        g_daily_intervaling = (g_daily_intervaling > 65535) ? 65535 : g_daily_intervaling;
    }
}

//开始日常扫描
void _begin_daily_scan()
{
    if( (enDONING == g_daily_is_working)&&(enIDLE != g_focus_is_working) )
    {
        //_up_station_errcode(QUERY_TAG_CONTINUE,RPT_SCANING,IS_SOCKET);
        return ;
    }
    g_daily_scaning_state = enBEGIN;
    g_scan_type = enDAILY;

    g_daily_count = 0;                               //日常扫描到的标签数量
    g_daily_index = PACKHEADER_LEN;                  //当前扫描到的标签数据索引
    memset(g_daily_tag,0,RX1_BUF_SIZE);              //日常扫描的RFID标签
   
    _set_reader_upparam(10,g_daily_scantime,g_threshold);//过滤单位时间内相同的标签
	_set_reader_coninue(50);
    
    g_daily_is_working = enDONING;
    g_daily_rec_state = enDONING;
}

//结束日常扫描
void _end_daily_scan()
{
    g_daily_scaning_state = enCOMPLETE;
    g_daily_rec_state = enCOMPLETE;
    _set_reader_stop(100);
}


//主线程中处理集中扫描部分
void _main_focus_scan(char source)
{
    if ((enDONING == g_focus_is_working))
    {
        if (enDONING == g_focus_scaning_state)
        {
            if ((g_focus_intervaling > g_focus_scantime))
            {
                //结束扫描
                _end_focus_scan();

            }
        }
        if (enCOMPLETE == g_focus_scaning_state)
        {
            if (g_focus_intervaling >= (g_focus_scantime + g_focus_interval))
            {
                g_daily_rec_state = enCOMPLETE;
                g_focus_is_working = enDEALING;
            }
        }
    }
    else if (enDEALING == g_focus_is_working)
    {
        //上报集中扫描的标签，全部上报
        bool bret = _up_station_focus_tag(source);
    }
    else if (enCOMPLETE == g_focus_is_working)
    {
        g_focus_is_working = enIDLE;
        g_focus_intervaling = 0;
    }
    else if (enIDLE == g_focus_is_working)
    {
        g_focus_intervaling = 0;
    }
}


//开始集中扫描
void _begin_focus_scan()
{    
    g_scan_type = enFOCUS;
    g_focus_is_working = enDONING;
    g_focus_scaning_state = enDONING;
    g_focus_rec_state = enDONING;
    g_focus_count = 0;
    g_focus_index = 0;
    memset(g_focus_tag,0,RX1_BUF_SIZE);


    g_focus_count = 0;                          //日常扫描到的标签数量
    g_focus_index = PACKHEADER_LEN;              //当前扫描到的标签数据索引
    memset(g_daily_tag, 0, RX1_BUF_SIZE);              //日常扫描的RFID标签

    _set_reader_upparam(10,g_focus_scantime,g_threshold);//过滤单位时间内相同的标签
	_set_reader_coninue(50);
    g_focus_intervaling = 0;
}

//结束集中扫描
void _end_focus_scan()
{
    _set_reader_stop(50);
    g_focus_scaning_state = enCOMPLETE;
// 输出结束集中扫描的日志回执
    _up_station_receipt(SET_STATION_BEGIN_FOCUS, RPT_STOP, IS_SOCKET);
}

//[上行]上报集中扫描到的所有标签数据集
bool _up_station_focus_tag( char source)
{
    bool bret  = true;

	g_focus_tag[0] = STATION_FRAME_HEADER;
    g_focus_tag[1] = STATION_MY_CMD;
    g_focus_tag[2] = SET_STATION_BEGIN_FOCUS;
    g_focus_tag[3] = (g_focus_count*TAG_LEN)/256;
    g_focus_tag[4] = (g_focus_count*TAG_LEN)%256;
    
    //上报集中扫描到的标签
    if(IS_SOCKET == source)
    {   
        // 检查连接状态
        if(g_online_state == enNO)
        {
            //ESP_LOGW(TAG, "[集中扫描] 设备离线，无法上报扫描结果，标签数量: %d", g_focus_count);
            bret = false;
        }
        else
        {
            int ret = send(g_connect_socket, g_focus_tag, g_focus_count*TAG_LEN+PACKHEADER_LEN , 0);
            if(ret < 0)
            {
                //ESP_LOGE(TAG, "[集中扫描] 扫描结果发送失败，标签数量: %d, 错误: %d (%s)",g_focus_count, errno, strerror(errno));
                g_online_state = enNO;
                g_rxtx_need_restart = true;
                //ESP_LOGI(TAG, "[集中扫描] 已触发重连机制");
                bret = false;
            }
            else
            {
                //ESP_LOGI(TAG, "[集中扫描] 扫描结果发送成功，标签数量: %d, 发送字节数: %d", 
                         //g_focus_count, ret);
            }
        }
    }
    else if(IS_SERIAL == source)
    {
        uart_write_bytes(UART_NUM_1, g_focus_tag, g_focus_count*TAG_LEN+PACKHEADER_LEN );
    }
    else
    {
        bret = false;
    }


    g_focus_is_working = enCOMPLETE;
    g_focus_scaning_state = enIDLE;
    g_focus_rec_state = enIDLE;
    g_scan_type = enDAILY;
    return bret;
}


//[上行]处理日常扫描到的标签
bool _up_station_daily_tag( char source )
{
    bool bret  = true;
    g_daily_scaning_state = enDEALING;

    int pcount[3];
    int pindex[3];
    int  ntotal =  ((g_daily_count*TAG_LEN)+PACKHEADER_LEN) + ((g_down_count*TAG_LEN)+PACKHEADER_LEN);

    // 内存分配和验证
    unsigned  char *addtag = (unsigned char*)malloc(ntotal);
    unsigned  char *normaltag = (unsigned char*)malloc(ntotal);
    unsigned  char *reducetag =  (unsigned char*)malloc(ntotal);

    // 检查内存分配是否成功
    if (!addtag || !normaltag || !reducetag) {
        // 释放已分配的内存
        if (addtag) free(addtag);
        if (normaltag) free(normaltag);
        if (reducetag) free(reducetag);

        g_daily_scaning_state = enIDLE;
        return false;
    }

    memset(addtag,0,ntotal);
    memset(normaltag,0,ntotal);
    memset(reducetag,0,ntotal);

     /********************注意下方两个函数顺序不能交换！！！！！********************/
    //处理日常扫描到的正常的和额外增加的标签数据
    if (!_deal_daily_addnormal_tag(addtag,normaltag,pcount,pindex, source )) {
        bret = false;
        goto cleanup; // 使用goto确保内存释放
    }

    //处理日常没有扫描到的标签数据
    if (!_deal_daily_reduce_tag(normaltag,reducetag,pcount,pindex, source )) {
        bret = false;
        goto cleanup;
    }

    /********************注意上方两个函数顺序不能交换！！！！！********************/

    int nindex = 0;// pindex[0];
    if( enMERGE&g_daily_scan_way)
    {
        if( enNORMAL&g_daily_scan_way)
        {
            bret = _up_station_addreduce_tag(addtag,reducetag,nindex,pindex,pcount,source);
        }
        else if( enIGNORE&g_daily_scan_way)
        {
            bret = _up_station_all_tag(addtag, normaltag,reducetag, nindex,pindex,pcount,source);
        }
    }
    else if(enSEPARATE&g_daily_scan_way)
    {
        if( enNORMAL&g_daily_scan_way)
        {
            bret =  _up_station_addtag    ( addtag, pindex[0], source );
            if (bret) {
                bret =  _up_station_reducetag ( reducetag, pindex[2], source );
            }
        }
        else if( enIGNORE&g_daily_scan_way)
        {
            // IGNORE模式：忽略正常标签，只上报变化（增加+减少）
            bret =  _up_station_addtag    ( addtag, pindex[0], source );
            if (bret) {
                bret =  _up_station_reducetag ( reducetag, pindex[2], source );
            }
        }
    }
    else
    {
        bret = false;
        //receipt g_daily rror to server
    }

cleanup:
    // 确保内存总是被释放
    free(addtag);
    free(normaltag);
    free(reducetag);

    //清空接收缓冲区
    memset(g_daily_tag, 0, RX1_BUF_SIZE);
    g_daily_scaning_state = enIDLE;
    g_daily_rec_state = enIDLE;
    g_daily_is_working = enCOMPLETE;
    g_scan_type = enUNUSED;

    return bret;
}

//处理日常扫描到的正常的和额外增加的标签数据
bool _deal_daily_addnormal_tag(unsigned char *addtag,unsigned char *normaltag,int* pcount,int *pindex,char source )
{
    bool bret = true;
    int addindex = PACKHEADER_LEN;
    int normalindex = PACKHEADER_LEN;
    int addcount = 0;
    int normalcount = 0;
    
    int i = 0;
    int j = 0;
    int it = 0;
    int jt = 0;

    int bcheck = false;
    for (;it < g_daily_count;it++)
    {
        jt = 0;
        for (;jt < g_down_count;jt++)
        {
            i = it * TAG_LEN + PACKHEADER_LEN;
            j = jt * TAG_LEN + PACKHEADER_LEN;
            if (g_daily_tag[i++] == g_down_tag[j++] &&
                g_daily_tag[i++] == g_down_tag[j++] &&
                g_daily_tag[i++] == g_down_tag[j++] &&
                g_daily_tag[i++] == g_down_tag[j++] &&
                g_daily_tag[i++] == g_down_tag[j++] &&
                g_daily_tag[i++] == g_down_tag[j++] &&
                g_daily_tag[i++] == g_down_tag[j++] &&
                g_daily_tag[i++] == g_down_tag[j++] &&
                g_daily_tag[i++] == g_down_tag[j++] &&
                g_daily_tag[i++] == g_down_tag[j++] &&
                g_daily_tag[i++] == g_down_tag[j++] &&
                g_daily_tag[i++] == g_down_tag[j++])
            {
                //在基站管理表中扫到的了，属于正常
                for(int kk=0;kk<TAG_LEN;kk++)
                {
                    normaltag[normalindex++] = g_down_tag[j - TAG_LEN+kk];
                }
                bcheck = true;
                normalcount++;
                continue;
            }
        }
        if (!bcheck)
        {
            i = it * TAG_LEN + PACKHEADER_LEN;
            //在正常表中找不到，属于增加的
            for (int k = 0; k < TAG_LEN; k++)
            {
                addtag[addindex++] = g_daily_tag[i++];
            }
            addcount++;
        }
        bcheck = false;
    }

    pindex[0] = addindex;
    pindex[1] = normalindex;
    pcount[0] = addcount;
    pcount[1] = normalcount;
   

    return bret;
}

//处理日常没有扫到的标签数据
bool _deal_daily_reduce_tag( unsigned char *normaltag,unsigned char *reducetag,int* pcount,int *pindex,char source )
{
    bool bret = true;
    int reduceindex = PACKHEADER_LEN;
    int reducecount = 0;
    int normalcount = pcount[1];
    int i = 0;
    int j = 0;
    int it = 0;
    int jt = 0;

    int bcheck = false;
    for (;it < g_down_count;it++)
    {
        jt = 0;
        // 根据上报模式选择不同的检测逻辑
        int search_count = (enIGNORE & g_daily_scan_way) ? g_daily_count : normalcount;
        unsigned char* search_data = (enIGNORE & g_daily_scan_way) ? g_daily_tag : normaltag;

        for (;jt < search_count;jt++)
        {
            j = it * TAG_LEN + PACKHEADER_LEN;
            i = jt * TAG_LEN + PACKHEADER_LEN;
            if (search_data[i++] == g_down_tag[j++] &&
                search_data[i++] == g_down_tag[j++] &&
                search_data[i++] == g_down_tag[j++] &&
                search_data[i++] == g_down_tag[j++] &&
                search_data[i++] == g_down_tag[j++] &&
                search_data[i++] == g_down_tag[j++] &&
                search_data[i++] == g_down_tag[j++] &&
                search_data[i++] == g_down_tag[j++] &&
                search_data[i++] == g_down_tag[j++] &&
                search_data[i++] == g_down_tag[j++] &&
                search_data[i++] == g_down_tag[j++] &&
                search_data[i++] == g_down_tag[j++])
            {
                //找到了这个管理标签
                bcheck = true;
                break;
            }
        }
        if (!bcheck)
        {
            j = it * TAG_LEN + PACKHEADER_LEN;
            //在本次扫描中找不到，属于减少的标签
            for (int k = 0; k < TAG_LEN; k++)
            {
                reducetag[reduceindex++] = g_down_tag[j++];
            }
            reducecount++;
        }
        bcheck = false;
    }
    pindex[2] = reduceindex;
    pcount[2] = reducecount;
 

    return bret;
}

//上报日常上报正常的标签
bool _up_stationg_normaltag( unsigned  char* recdata,int nlen,char source  )
{
    bool bret  = true;
    if(nlen < (TAG_LEN+PACKHEADER_LEN))
    {
        //_up_station_receipt(UP_STATION_NORMALTAG,RPT_NOTAG,source);
        return false;
    }   
    int ii = 0;
	recdata[ii++] = STATION_FRAME_HEADER;
    recdata[ii++] = STATION_MY_CMD;
    recdata[ii++] = UP_STATION_NORMALTAG;
    recdata[ii++] = (nlen-PACKHEADER_LEN)/256;
    recdata[ii++] = (nlen-PACKHEADER_LEN)%256;
     
    if(IS_SOCKET == source)
    {
        // 检查连接状态
        if(g_online_state == enNO)
        {
            //ESP_LOGW(TAG, "[标签上报] 设备离线，无法上报正常标签");
            bret = false;
        }
        else
        {
            int ret = send(g_connect_socket, recdata, nlen , 0);
            if(ret < 0)
            {
                //ESP_LOGE(TAG, "[标签上报] 正常标签发送失败，错误: %d (%s)", errno, strerror(errno));
                g_online_state = enNO;
                g_rxtx_need_restart = true;
                //ESP_LOGI(TAG, "[标签上报] 已触发重连机制");
                bret = false;
            }
            else
            {
                //ESP_LOGI(TAG, "[标签上报] 正常标签发送成功，发送字节数: %d", ret);
            }
        }
    }
    else if(IS_SERIAL == source)
    {
        uart_write_bytes(UART_NUM_1, recdata, nlen );
    }

    return bret;
}
    

//上报日常扫到的多的标签
bool _up_station_addtag( unsigned char* recdata,int nlen, char source )
{
    bool bret  = true;

    // 参数验证
    if (!recdata || nlen < (TAG_LEN+PACKHEADER_LEN) || nlen > RX1_BUF_SIZE)
    {
        //_up_station_receipt(UP_STATION_ADDTAG,RPT_NOTAG,source);
        return false;
    }

    int ii = 0;
	recdata[ii++] = STATION_FRAME_HEADER;
    recdata[ii++] = STATION_MY_CMD;
    recdata[ii++] = UP_STATION_ADDTAG;
    recdata[ii++] = (nlen-PACKHEADER_LEN)/256;
    recdata[ii++] = (nlen-PACKHEADER_LEN)%256;


    if(IS_SOCKET == source)
    {
        // 检查网络连接状态
        if(g_online_state == enNO)
        {
            bret = false;
        }
        else
        {
            int ret = send(g_connect_socket, recdata, nlen, 0);
            if(ret < 0)
            {
                g_online_state = enNO;
                g_rxtx_need_restart = true;
                bret = false;
            }
        }
    }
    else if(IS_SERIAL == source)
    {
        uart_write_bytes(UART_NUM_1, recdata, nlen );
    }

    return bret;
}


//上报日常没有扫到的标签
bool _up_station_reducetag( unsigned char* recdata,int nlen, char source )
{
    bool bret  = true;

    // 参数验证
    if (!recdata || nlen < (TAG_LEN+PACKHEADER_LEN) || nlen > RX1_BUF_SIZE)
    {
        //_up_station_receipt(UP_STATION_REDUCETAG,RPT_NOTAG,source);
        return false;
    }

    int ii = 0;
	recdata[ii++] = STATION_FRAME_HEADER;
    recdata[ii++] = STATION_MY_CMD;
    recdata[ii++] = UP_STATION_REDUCETAG;
    recdata[ii++] = (nlen-PACKHEADER_LEN)/256;
    recdata[ii++] = (nlen-PACKHEADER_LEN)%256;

    if(IS_SOCKET == source)
    {
        // 检查网络连接状态
        if(g_online_state == enNO)
        {
            bret = false;
        }
        else
        {
            int ret = send(g_connect_socket, recdata, nlen, 0);
            if(ret < 0)
            {
                g_online_state = enNO;
                g_rxtx_need_restart = true;
                bret = false;
            }
        }
    }
    else if(IS_SERIAL == source)
    {
        uart_write_bytes(UART_NUM_1, recdata, nlen );
    }

    return bret;
}



//上报日常扫描的增加和减少的标签
bool _up_station_addreduce_tag(unsigned  char *addtag, unsigned  char *reducetag,int nindex,int *pindex,int *pcount,char source)
{
    bool bret = true;

    // 参数验证
    if (!addtag || !reducetag || !pindex || !pcount) {
        return false;
    }

    int nreducecount = (pcount[2]) > MAX_COUNT ? MAX_COUNT : pcount[2];
    int nreduceindex = (pindex[2]) > RX1_BUF_SIZE ? RX1_BUF_SIZE : pindex[2];
    int ntxlen = pindex[0] + pindex[2] - PACKHEADER_LEN + 4;
    int ntxtotal = pcount[0] + pcount[2];

    if ((ntxlen > RX1_BUF_SIZE) || (ntxtotal > MAX_COUNT))
    {
        ntxtotal = MAX_COUNT;
        ntxlen = MAX_COUNT * TAG_LEN + PACKHEADER_LEN;
    }
    if(ntxlen < (TAG_LEN+PACKHEADER_LEN))
    {
        //_up_station_receipt(UP_STATION_ADD_REDUCE,RPT_NOTAG,source);
        return false;
    }

    int ii = 0;
    memset(g_daily_tag, 0, RX1_BUF_SIZE);
    g_daily_tag[ii++] = STATION_FRAME_HEADER;
    g_daily_tag[ii++] = STATION_MY_CMD;
    g_daily_tag[ii++] = 0xcd;
    g_daily_tag[ii++] = ntxlen / 256;
    g_daily_tag[ii++] = ntxlen % 256;

    // 处理减少的标签
    g_daily_tag[ii++] = enREDUCE;
    g_daily_tag[ii++] = 0xFF & nreducecount;

    // 修复循环逻辑：使用正确的索引计算，避免双重递增
    int tag_index = 0;
    for (tag_index = 0; tag_index < nreducecount && (ii + TAG_LEN) < RX1_BUF_SIZE; tag_index++)
    {
        int src_offset = PACKHEADER_LEN + tag_index * TAG_LEN;
        if (src_offset + TAG_LEN > nreduceindex)
            break;

        for (int j = 0; j < TAG_LEN; j++)
        {
            g_daily_tag[ii++] = reducetag[src_offset + j];
        }
    }

    // 处理增加的标签
    g_daily_tag[ii++] = enADD;
    g_daily_tag[ii++] = 0xFF & (ntxtotal - nreducecount);

    int naddcount = ntxtotal - nreducecount;
    for (tag_index = 0; tag_index < naddcount && (ii + TAG_LEN) < RX1_BUF_SIZE; tag_index++)
    {
        int src_offset = PACKHEADER_LEN + tag_index * TAG_LEN;
        if (src_offset + TAG_LEN > pindex[0])
            break;

        for (int j = 0; j < TAG_LEN; j++)
        {
            g_daily_tag[ii++] = addtag[src_offset + j];
        }
    }

    //上报日常扫描到的标签
    int actual_len = ii; // 使用实际写入的长度
    if(IS_SOCKET == source)
    {
        // 检查网络连接状态
        if(g_online_state == enNO)
        {
            bret = false;
        }
        else
        {
            int ret = send(g_connect_socket, g_daily_tag, actual_len, 0);
            if(ret < 0)
            {
                g_online_state = enNO;
                g_rxtx_need_restart = true;
                bret = false;
            }
        }
    }
    else if(IS_SERIAL == source)
    {
        uart_write_bytes(UART_NUM_1, g_daily_tag, actual_len);
    }
    else
    {
        _up_station_receipt(UP_STATION_ADD_REDUCE,RPT_FAILED,source);
        bret = false;
    }

    return bret;
}
//上报日常扫描的所有标签
bool _up_station_all_tag(unsigned  char *addtag, unsigned  char *normaltag,unsigned  char *reducetag,int nindex,int *pindex,int *pcount,char source)
{
    bool bret = true;
    int ntxlen = pindex[0] + pindex[2] + pindex[1] - 2 * PACKHEADER_LEN + 6;
    int ntxtotal = pcount[0] + pcount[2] + pcount[1];
    ntxlen = ntxlen < RX1_BUF_SIZE ? ntxlen : RX1_BUF_SIZE;
    
    if(ntxlen < (TAG_LEN+PACKHEADER_LEN))
    {
        //_up_station_receipt(UP_STATION_DAILY_ALL,RPT_NOTAG,source);
        return false;
    } 

    int ii = 0;
    memset(g_daily_tag, 0, RX1_BUF_SIZE);
    g_daily_tag[ii++] = STATION_FRAME_HEADER;
    g_daily_tag[ii++] = STATION_MY_CMD;
    g_daily_tag[ii++] = 0xcd;
    g_daily_tag[ii++] = ntxlen / 256;
    g_daily_tag[ii++] = ntxlen % 256;
    int nreduceindex = (pindex[2]);
    int normalindex = (pindex[1]);
    int naddindex = (pindex[0]);
    int i = 0;
    g_daily_tag[ii++] = enREDUCE;
    g_daily_tag[ii++] = 0xFF & nreduceindex;
    for (i = PACKHEADER_LEN;i < nreduceindex;i++)
    {
        for (int j = 0;j < TAG_LEN;j++)
        {
            g_daily_tag[ii++] = reducetag[i++];
        }
    }
    g_daily_tag[ii++] = enNORMAL;
    g_daily_tag[ii++] = 0xFF & normalindex;
    for (i = PACKHEADER_LEN;i < normalindex;i++)
    {
        for (int j = 0;j < TAG_LEN;j++)
        {
            g_daily_tag[ii++] = normaltag[i++];
        }
    }
    g_daily_tag[ii++] = enADD;
    g_daily_tag[ii++] = 0xFF & naddindex;
    for (i = PACKHEADER_LEN;i < naddindex;i++)
    {
        for (int j = 0;j < TAG_LEN;j++)
        {
            g_daily_tag[ii++] = addtag[i++];
        }
    }
      //上报日常扫描到的标签
    if(IS_SOCKET == source)
    {
        send(g_connect_socket, g_daily_tag, ntxtotal*TAG_LEN+PACKHEADER_LEN , 0);    
    }
    else if(IS_SERIAL == source)
    {
        uart_write_bytes(UART_NUM_1, g_daily_tag, ntxtotal*TAG_LEN+PACKHEADER_LEN );
    }
    else
    {
        _up_station_receipt(UP_STATION_DAILY_ALL,RPT_FAILED,source);
        bret = false;
    }
 
    return bret;
}




//为工作参数缓冲区填充数据
int copy_station_params_to_buf(unsigned char* txbuf, int nlen)
{    
    int ii = 0;
      
    txbuf[ii++] = STATION_FRAME_HEADER;
    txbuf[ii++] = STATION_MY_CMD;
    txbuf[ii++] = GET_STATION_PARAM;
    ii = ii + 2;//sizeof(length) 
    for (int j=0;j < (DEVID_LEN-1);j++)
    {
        txbuf[ii++] = g_device_id[j];
    }
    txbuf[ii++] = g_down_count;
    txbuf[ii++] = (g_heart_interval)/256;
    txbuf[ii++] = (g_heart_interval)%256;
    txbuf[ii++] = g_heart_state;
    txbuf[ii++] = (g_daily_interval)/256;
    txbuf[ii++] = (g_daily_interval)%256;
    txbuf[ii++] = (g_daily_scantime)/256;
    txbuf[ii++] = (g_daily_scantime)%256;
    txbuf[ii++] = (g_focus_interval)/256;
    txbuf[ii++] = (g_focus_interval)%256;
    txbuf[ii++] = (g_focus_scantime)/256;
    txbuf[ii++] = (g_focus_scantime)%256;
    txbuf[ii++] = g_daily_scan_way;
    txbuf[ii++] = g_transparent_transmission;
    txbuf[ii++] = g_daily_scan_state;
    txbuf[ii++] = g_focus_is_working;
    txbuf[ii++] = g_update_state;
    txbuf[ii++] = g_threshold;
    txbuf[ii++] = g_idle_state;
    txbuf[ii++] = (g_idle_time)/256;
    txbuf[ii++] = (g_idle_time)%256;
    txbuf[ii++] = (g_scan_time)/256;
    txbuf[ii++] = (g_scan_time)%256;
    txbuf[ii++] = g_protocol_version;
    txbuf[ii++] = g_beep_state;
    txbuf[ii++] = g_economizen;
    txbuf[3] = (ii-PACKHEADER_LEN)/256;
    txbuf[4] = (ii-PACKHEADER_LEN)%256;

    return ii;
}


/**********************************http部分*************************************/
//HTTP网络事件回调
esp_err_t _http_event_handler(esp_http_client_event_t* evt)
{
    switch (evt->event_id) {
    case HTTP_EVENT_ERROR:
        ESP_LOGD(TAG, "HTTP_EVENT_ERROR");
        break;
    case HTTP_EVENT_ON_CONNECTED:
        ESP_LOGD(TAG, "HTTP_EVENT_ON_CONNECTED");
        break;
    case HTTP_EVENT_HEADER_SENT:
        ESP_LOGD(TAG, "HTTP_EVENT_HEADER_SENT");
        break;
    case HTTP_EVENT_ON_HEADER:
        ESP_LOGD(TAG, "HTTP_EVENT_ON_HEADER, key=%s, value=%s", evt->header_key, evt->header_value);
        break;
    case HTTP_EVENT_ON_DATA:
        ESP_LOGD(TAG, "HTTP_EVENT_ON_DATA, len=%d", evt->data_len);
        break;
    case HTTP_EVENT_ON_FINISH:
        ESP_LOGD(TAG, "HTTP_EVENT_ON_FINISH");
        break;
    case HTTP_EVENT_DISCONNECTED:
        ESP_LOGD(TAG, "HTTP_EVENT_DISCONNECTED");
        break;
    default:
        ESP_LOGD(TAG, "HTTP_EVENT_ERROR");
        break;
    }
    return ESP_OK;
}
//远程更新固件线程任务
void _ota_http_task(void* pvParameter)
{
    // bool ret = true; // 未使用，注释掉

    esp_http_client_config_t http_config = {
        .url = g_https_url,
        //.url = CONFIG_EXAMPLE_FIRMWARE_UPGRADE_URL,
        .cert_pem = (char *)g_server_cert_pem_start,
        .event_handler = _http_event_handler,
        //.keep_alive_enable = true,
        //.skip_cert_verify = true, 
        //.method = HTTP_METHOD_POST,
        .timeout_ms = 50000, // 设置超时时间为50秒
        .cert_pem = NULL,
    };
    // esp_https_ota_config_t ota_config = {
    //     .http_config = &http_config,
    // }; // 未使用，注释掉

    while (1)
    {
        if (g_update_state == enYES)
        {
            esp_err_t bret = esp_https_ota(&http_config);
            if (bret == ESP_OK)
            {
                esp_restart();
            }
            // else if (ret == ESP_ERR_OTA_NEW_IMAGE_SAME)
            // {
            //     ESP_LOGI(TAG, "OTA upgrade skipped because the new firmware image is the same as the running one.");
            // }
            // else if (ret == ESP_ERR_OTA_NEW_IMAGE_INVALID)
            // {
            //     ESP_LOGE(TAG, "OTA upgrade failed because the new firmware image is invalid.");
            // }
            else
            {
                //ESP_LOGE(TAG, "OTA upgrade failed with error code: 0x%x", ret);
            }
            g_update_state = enNO;
        }
        vTaskDelay(1000 / portTICK_PERIOD_MS);
    }

}

//[下行]清理标签数据
bool _set_station_clear_nvs( unsigned char* recdata, unsigned short index, unsigned short nlen ,char source)
{
    bool bret = true;

    // 检查命令格式：FEEFFE0000 (5字节)
    if( (FRAME_LEN!=index)||(PACKHEADER_LEN!=nlen) )
    {
        _up_station_receipt(SET_STATION_CLEAR_NVS,RPT_RECLEN,source);
        return false;
    }

    int i = index;
    unsigned char high = recdata[i++];
    unsigned char low = recdata[i++];
    int nrxdtlen = (high<<8)|low;

    // 数据长度应该为0
    if( 0 != nrxdtlen )
    {
        _up_station_receipt(SET_STATION_CLEAR_NVS,RPT_LENGTH,source);
        return false;
    }

    ESP_LOGI(TAG, "Clearing tag data...");

    // 只清理标签相关的NVS数据
    nvs_handle_t my_handle;
    esp_err_t err = nvs_open(g_namespace, NVS_READWRITE, &my_handle);
    if (err != ESP_OK) {
        ESP_LOGE(TAG, "Failed to open NVS for clearing tag data (%s)", esp_err_to_name(err));
        _up_station_receipt(SET_STATION_CLEAR_NVS,RPT_WFLASH,source);
        return false;
    }

    // 只删除标签相关的键值对
    bool success = true;

    // 删除下载标签数据（这是主要的标签存储）
    err = nvs_erase_key(my_handle, g_sdown_count);
    if (err != ESP_OK && err != ESP_ERR_NVS_NOT_FOUND) {
        ESP_LOGE(TAG, "Failed to erase down_count (%s)", esp_err_to_name(err));
        success = false;
    }

    err = nvs_erase_key(my_handle, g_sdown_tag);
    if (err != ESP_OK && err != ESP_ERR_NVS_NOT_FOUND) {
        ESP_LOGE(TAG, "Failed to erase down_tag (%s)", esp_err_to_name(err));
        success = false;
    }

    if (success) {
        err = nvs_commit(my_handle);
        if (err == ESP_OK) {
            ESP_LOGI(TAG, "Successfully cleared tag data from NVS");

            // 重置内存中的标签数据
            g_down_count = 0;
            g_down_index = PACKHEADER_LEN;
            memset(g_down_tag, 0, RX1_BUF_SIZE);

            g_daily_count = 0;
            g_daily_index = PACKHEADER_LEN;
            memset(g_daily_tag, 0, RX1_BUF_SIZE);

            g_focus_count = 0;
            g_focus_index = PACKHEADER_LEN;
            memset(g_focus_tag, 0, RX1_BUF_SIZE);

            _up_station_receipt(SET_STATION_CLEAR_NVS,RPT_SUCCESS,source);
            bret = true;
        } else {
            ESP_LOGE(TAG, "Failed to commit tag data clear (%s)", esp_err_to_name(err));
            _up_station_receipt(SET_STATION_CLEAR_NVS,RPT_WFLASH,source);
            bret = false;
        }
    } else {
        ESP_LOGE(TAG, "Failed to clear some tag data");
        _up_station_receipt(SET_STATION_CLEAR_NVS,RPT_WFLASH,source);
        bret = false;
    }

    nvs_close(my_handle);
    return bret;
}

